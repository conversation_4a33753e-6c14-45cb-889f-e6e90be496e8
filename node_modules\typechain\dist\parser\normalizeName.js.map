{"version": 3, "file": "normalizeName.js", "sourceRoot": "", "sources": ["../../src/parser/normalizeName.ts"], "names": [], "mappings": ";;;AAAA,mCAAmC;AAEnC;;GAEG;AACH,SAAgB,aAAa,CAAC,OAAe;IAC3C,MAAM,eAAe,GAA8B;QACjD,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;QAC7B,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;QAC5B,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QACtE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;QAC1B,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;QAC5B,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,mBAAU,EAAC,CAAC,CAAC;KACrB,CAAA;IAED,MAAM,SAAS,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IAEjE,IAAI,SAAS,KAAK,EAAE,EAAE;QACpB,MAAM,IAAI,KAAK,CAAC,+CAA+C,OAAO,EAAE,CAAC,CAAA;KAC1E;IAED,OAAO,SAAS,CAAA;AAClB,CAAC;AAjBD,sCAiBC"}