{"version": 3, "file": "changeEtherBalance.js", "sourceRoot": "", "sources": ["../src/internal/changeEtherBalance.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,oCAAuC;AACvC,oDAAkD;AAClD,4CAA8C;AAC9C,2CAA2D;AAC3D,mCAAuE;AAEvE,SAAgB,yBAAyB,CACvC,SAA+B,EAC/B,SAAyB;IAEzB,SAAS,CAAC,SAAS,CACjB,wCAA4B,EAC5B,UAEE,OAA6B,EAC7B,aAA2D,EAC3D,OAA8B;QAE9B,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAmB,CAAC;QACzD,2EAA2E;QAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACpC,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QAE1B,IAAA,mCAA2B,EACzB,IAAI,EACJ,wCAA4B,EAC5B,SAAS,CACV,CAAC;QAEF,MAAM,kBAAkB,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,CAGjD,EAAE,EAAE;YACH,MAAM,MAAM,GAAG,IAAA,mBAAW,EAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;YAExD,IAAI,OAAO,aAAa,KAAK,UAAU,EAAE;gBACvC,MAAM,CACJ,aAAa,CAAC,YAAY,CAAC,EAC3B,yCAAyC,OAAO,8DAA8D,YAAY,CAAC,QAAQ,EAAE,OAAO,EAC5I,yCAAyC,OAAO,+DAA+D,YAAY,CAAC,QAAQ,EAAE,OAAO,CAC9I,CAAC;aACH;iBAAM;gBACL,MAAM,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC;gBAC/C,MAAM,CACJ,YAAY,KAAK,cAAc,EAC/B,kCAAkC,OAAO,kBAAkB,aAAa,CAAC,QAAQ,EAAE,2BAA2B,YAAY,CAAC,QAAQ,EAAE,MAAM,EAC3I,kCAAkC,OAAO,sBAAsB,aAAa,CAAC,QAAQ,EAAE,kBAAkB,CAC1G,CAAC;aACH;QACH,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC;YACjC,gBAAgB,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;YAC3C,IAAA,sBAAY,EAAC,OAAO,CAAC;SACtB,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC5B,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACrD,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACvD,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC,CACF,CAAC;AACJ,CAAC;AAvDD,8DAuDC;AAEM,KAAK,UAAU,gBAAgB,CACpC,WAG8D,EAC9D,OAA6B,EAC7B,OAA8B;IAE9B,MAAM,GAAG,GAAG,wDAAa,SAAS,GAAC,CAAC;IACpC,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC;IAEtC,IAAI,UAA+B,CAAC;IAEpC,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE;QACrC,UAAU,GAAG,MAAM,WAAW,EAAE,CAAC;KAClC;SAAM;QACL,UAAU,GAAG,MAAM,WAAW,CAAC;KAChC;IAED,MAAM,SAAS,GAAG,MAAM,UAAU,CAAC,IAAI,EAAE,CAAC;IAC1C,IAAA,uBAAe,EAAC,SAAS,EAAE,WAAW,CAAC,CAAC;IACxC,MAAM,aAAa,GAAG,SAAS,CAAC,WAAW,CAAC;IAE5C,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,oBAAoB,EAAE;QACtD,SAAS,CAAC,SAAS;QACnB,KAAK;KACN,CAAC,CAAC;IAEH,IAAA,cAAM,EACJ,KAAK,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAC/B,KAAK,EACL,sCAAsC,CACvC,CAAC;IAEF,MAAM,OAAO,GAAG,MAAM,IAAA,sBAAY,EAAC,OAAO,CAAC,CAAC;IAE5C,MAAM,eAAe,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE;QAC5D,OAAO;QACP,KAAK,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;KAClC,CAAC,CAAC;IAEH,MAAM,gBAAgB,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,gBAAgB,EAAE;QAC7D,OAAO;QACP,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;KACxC,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC;IAC7C,MAAM,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAE/C,IAAI,OAAO,EAAE,UAAU,KAAK,IAAI,IAAI,OAAO,KAAK,UAAU,CAAC,IAAI,EAAE;QAC/D,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;QACpC,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;QAClC,MAAM,KAAK,GAAG,QAAQ,GAAG,OAAO,CAAC;QAEjC,OAAO,YAAY,GAAG,KAAK,GAAG,aAAa,CAAC;KAC7C;SAAM;QACL,OAAO,YAAY,GAAG,aAAa,CAAC;KACrC;AACH,CAAC;AA1DD,4CA0DC"}