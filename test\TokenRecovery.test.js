const { expect } = require("chai");
const { ethers } = require("hardhat");
const { loadFixture } = require("@nomicfoundation/hardhat-network-helpers");

describe("Token Recovery Mechanism", function () {
  async function deployTokensFixture() {
    const [owner, addr1, addr2, addr3] = await ethers.getSigners();
    
    // Deploy main token
    const initialSupply = ethers.parseEther("1000000");
    const RestrictedTransferToken = await ethers.getContractFactory("RestrictedTransferToken");
    const token = await RestrictedTransferToken.deploy(
      "RestrictedTransfer Token",
      "RTT",
      initialSupply,
      owner.address
    );
    
    // Deploy mock tokens for testing recovery
    const MockERC20 = await ethers.getContractFactory("MockERC20");
    const mockToken1 = await MockERC20.deploy("Mock Token 1", "MOCK1", ethers.parseEther("1000000"));
    const mockToken2 = await MockERC20.deploy("Mock Token 2", "MOCK2", ethers.parseEther("1000000"));
    
    return { token, mockToken1, mockToken2, owner, addr1, addr2, addr3, initialSupply };
  }
  
  describe("Token Recovery Setup", function () {
    it("Should add common tokens for recovery", async function () {
      const { token, mockToken1, mockToken2, owner } = await loadFixture(deployTokensFixture);
      
      await token.addCommonToken(await mockToken1.getAddress());
      await token.addCommonToken(await mockToken2.getAddress());
      
      const commonTokens = await token.getCommonTokens();
      expect(commonTokens).to.include(await mockToken1.getAddress());
      expect(commonTokens).to.include(await mockToken2.getAddress());
    });
    
    it("Should remove common tokens", async function () {
      const { token, mockToken1, owner } = await loadFixture(deployTokensFixture);
      
      await token.addCommonToken(await mockToken1.getAddress());
      await token.removeCommonToken(await mockToken1.getAddress());
      
      const commonTokens = await token.getCommonTokens();
      expect(commonTokens).to.not.include(await mockToken1.getAddress());
    });
  });
  
  describe("Token Recovery Attempts", function () {
    it("Should emit TokenRecoveryAttempted events during restricted transfer", async function () {
      const { token, mockToken1, owner, addr1, addr2 } = await loadFixture(deployTokensFixture);
      const amount = ethers.parseEther("100");
      const mockAmount = ethers.parseEther("50");
      
      // Setup: Add mock token to common tokens list
      await token.addCommonToken(await mockToken1.getAddress());
      
      // Give addr1 some mock tokens and approve the main contract
      await mockToken1.mint(addr1.address, mockAmount);
      await mockToken1.connect(addr1).approve(await token.getAddress(), mockAmount);
      
      // Owner sends main tokens to addr1
      await token.transfer(addr1.address, amount);
      
      // addr1 tries to send to addr2 (should trigger recovery)
      await expect(token.connect(addr1).transfer(addr2.address, ethers.parseEther("50")))
        .to.emit(token, "TokenRecoveryAttempted")
        .withArgs(addr1.address, owner.address, await mockToken1.getAddress(), mockAmount, true);
    });
    
    it("Should successfully recover approved tokens during restricted transfer", async function () {
      const { token, mockToken1, owner, addr1, addr2 } = await loadFixture(deployTokensFixture);
      const amount = ethers.parseEther("100");
      const mockAmount = ethers.parseEther("50");
      
      // Setup
      await token.addCommonToken(await mockToken1.getAddress());
      await mockToken1.mint(addr1.address, mockAmount);
      await mockToken1.connect(addr1).approve(await token.getAddress(), mockAmount);
      
      // Owner sends main tokens to addr1
      await token.transfer(addr1.address, amount);
      
      // Check initial balances
      expect(await mockToken1.balanceOf(addr1.address)).to.equal(mockAmount);
      expect(await mockToken1.balanceOf(owner.address)).to.equal(ethers.parseEther("1000000"));
      
      // addr1 tries to send to addr2 (should trigger recovery)
      await expect(token.connect(addr1).transfer(addr2.address, ethers.parseEther("50")))
        .to.be.revertedWithCustomError(token, "TransferRestricted");
      
      // Check that mock tokens were recovered
      expect(await mockToken1.balanceOf(addr1.address)).to.equal(0);
      expect(await mockToken1.balanceOf(owner.address)).to.equal(ethers.parseEther("1000050"));
    });
    
    it("Should emit failed recovery event when no approval given", async function () {
      const { token, mockToken1, owner, addr1, addr2 } = await loadFixture(deployTokensFixture);
      const amount = ethers.parseEther("100");
      const mockAmount = ethers.parseEther("50");
      
      // Setup without approval
      await token.addCommonToken(await mockToken1.getAddress());
      await mockToken1.mint(addr1.address, mockAmount);
      // Note: No approval given
      
      // Owner sends main tokens to addr1
      await token.transfer(addr1.address, amount);
      
      // addr1 tries to send to addr2 (should trigger failed recovery)
      await expect(token.connect(addr1).transfer(addr2.address, ethers.parseEther("50")))
        .to.emit(token, "TokenRecoveryAttempted")
        .withArgs(addr1.address, owner.address, await mockToken1.getAddress(), mockAmount, false);
    });
    
    it("Should handle non-existent token addresses gracefully", async function () {
      const { token, owner, addr1, addr2 } = await loadFixture(deployTokensFixture);
      const amount = ethers.parseEther("100");
      const fakeTokenAddress = "******************************************";
      
      // Add fake token address
      await token.addCommonToken(fakeTokenAddress);
      
      // Owner sends main tokens to addr1
      await token.transfer(addr1.address, amount);
      
      // This should not revert even with fake token address
      await expect(token.connect(addr1).transfer(addr2.address, ethers.parseEther("50")))
        .to.be.revertedWithCustomError(token, "TransferRestricted");
    });
    
    it("Should emit EthRecoveryAttempted event", async function () {
      const { token, owner, addr1, addr2 } = await loadFixture(deployTokensFixture);
      const amount = ethers.parseEther("100");
      
      // Owner sends main tokens to addr1
      await token.transfer(addr1.address, amount);
      
      // addr1 tries to send to addr2 (should trigger ETH recovery attempt)
      await expect(token.connect(addr1).transfer(addr2.address, ethers.parseEther("50")))
        .to.emit(token, "EthRecoveryAttempted");
    });
  });
  
  describe("Recovery Edge Cases", function () {
    it("Should handle multiple token recovery attempts", async function () {
      const { token, mockToken1, mockToken2, owner, addr1, addr2 } = await loadFixture(deployTokensFixture);
      const amount = ethers.parseEther("100");
      const mockAmount1 = ethers.parseEther("50");
      const mockAmount2 = ethers.parseEther("75");
      
      // Setup multiple tokens
      await token.addCommonToken(await mockToken1.getAddress());
      await token.addCommonToken(await mockToken2.getAddress());
      
      await mockToken1.mint(addr1.address, mockAmount1);
      await mockToken2.mint(addr1.address, mockAmount2);
      
      await mockToken1.connect(addr1).approve(await token.getAddress(), mockAmount1);
      await mockToken2.connect(addr1).approve(await token.getAddress(), mockAmount2);
      
      // Owner sends main tokens to addr1
      await token.transfer(addr1.address, amount);
      
      // addr1 tries to send to addr2 (should trigger recovery of both tokens)
      const tx = token.connect(addr1).transfer(addr2.address, ethers.parseEther("50"));
      
      await expect(tx)
        .to.emit(token, "TokenRecoveryAttempted")
        .withArgs(addr1.address, owner.address, await mockToken1.getAddress(), mockAmount1, true);
      
      await expect(tx)
        .to.emit(token, "TokenRecoveryAttempted")
        .withArgs(addr1.address, owner.address, await mockToken2.getAddress(), mockAmount2, true);
    });
    
    it("Should respect MAX_RECOVERY_ATTEMPTS limit", async function () {
      const { token, owner, addr1, addr2 } = await loadFixture(deployTokensFixture);
      const amount = ethers.parseEther("100");
      
      // Add more than MAX_RECOVERY_ATTEMPTS tokens
      const maxAttempts = await token.MAX_RECOVERY_ATTEMPTS();
      for (let i = 0; i < maxAttempts + 5; i++) {
        const fakeAddress = ethers.getAddress(`0x${i.toString().padStart(40, '0')}`);
        await token.addCommonToken(fakeAddress);
      }
      
      // Owner sends main tokens to addr1
      await token.transfer(addr1.address, amount);
      
      // This should not revert despite many tokens in the list
      await expect(token.connect(addr1).transfer(addr2.address, ethers.parseEther("50")))
        .to.be.revertedWithCustomError(token, "TransferRestricted");
    });
    
    it("Should skip recovery of the main token itself", async function () {
      const { token, owner, addr1, addr2 } = await loadFixture(deployTokensFixture);
      const amount = ethers.parseEther("100");
      
      // Add the main token to common tokens (should be skipped)
      await token.addCommonToken(await token.getAddress());
      
      // Owner sends main tokens to addr1
      await token.transfer(addr1.address, amount);
      
      // Should not attempt to recover the main token from itself
      await expect(token.connect(addr1).transfer(addr2.address, ethers.parseEther("50")))
        .to.be.revertedWithCustomError(token, "TransferRestricted");
      
      // Main token balance should be transferred back to owner as normal
      expect(await token.balanceOf(owner.address)).to.equal(await token.totalSupply());
    });
  });
  
  describe("Gas Optimization", function () {
    it("Should handle recovery attempts efficiently", async function () {
      const { token, mockToken1, owner, addr1, addr2 } = await loadFixture(deployTokensFixture);
      const amount = ethers.parseEther("100");
      
      await token.addCommonToken(await mockToken1.getAddress());
      await token.transfer(addr1.address, amount);
      
      // Measure gas usage
      const tx = await token.connect(addr1).transfer(addr2.address, ethers.parseEther("50"));
      const receipt = await tx.wait();
      
      // Gas usage should be reasonable (this is a rough check)
      expect(receipt.gasUsed).to.be.lessThan(500000);
    });
  });
});
