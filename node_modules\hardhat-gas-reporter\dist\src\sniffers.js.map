{"version": 3, "file": "sniffers.js", "sourceRoot": "", "sources": ["../../src/sniffers.ts"], "names": [], "mappings": ";;;AAEA,SAAgB,8BAA8B,CAAC,WAAgB;IAC7D,OAAO,KAAK,WAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE;QAC9C,UAAU;QACV,IAAI,IAAI,CAAC,MAAM,KAAK,2BAA2B,EAAE;YAC/C,MAAM,OAAO,GAAQ,MAAM,MAAM,CAAC;YAClC,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,MAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,eAAe,CAAA,EAAE;gBAC/C,MAAM,EAAE,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC;oBAChC,MAAM,EAAE,0BAA0B;oBAClC,MAAM,EAAE,CAAC,OAAO,CAAC,eAAe,CAAC;iBAClC,CAAC,CAAC;gBACH,MAAM,WAAW,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;aAC9D;YAED,uFAAuF;SACxF;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,0BAA0B,EAAE;YACrD,MAAM,OAAO,GAAQ,MAAM,QAAQ,CAAC,OAAO,CAAC;gBAC1C,MAAM,EAAE,2BAA2B;gBACnC,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB,CAAC,CAAC;YACH,MAAM,EAAE,GAAG,MAAM,MAAM,CAAC;YACxB,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAE;gBACnB,MAAM,WAAW,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;aAC9D;YAED,uFAAuF;YACvF,0EAA0E;SAC3E;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,wBAAwB,EAAE;YACnD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC;YAE5B,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;gBAC9B,MAAM,EAAE,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC;oBAChC,MAAM,EAAE,0BAA0B;oBAClC,MAAM,EAAE,CAAC,MAAM,CAAC;iBACjB,CAAC,CAAC;gBACH,MAAM,OAAO,GAAQ,MAAM,QAAQ,CAAC,OAAO,CAAC;oBAC1C,MAAM,EAAE,2BAA2B;oBACnC,MAAM,EAAE,CAAC,MAAM,CAAC;iBACjB,CAAC,CAAC;gBAEH,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAE;oBACnB,MAAM,WAAW,CAAC,WAAW,CAAC,iBAAiB,CAC7C,OAAO,EACP,EAAE,CACH,CAAC;iBACH;aACF;YACD,OAAO;SACR;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,qBAAqB,EAAE;YAChD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC;YAE5B,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;gBAC9B,MAAM,EAAE,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC;oBAChC,MAAM,EAAE,0BAA0B;oBAClC,MAAM,EAAE,CAAC,MAAM,CAAC;iBACjB,CAAC,CAAC;gBACH,MAAM,OAAO,GAAQ,MAAM,QAAQ,CAAC,OAAO,CAAC;oBAC1C,MAAM,EAAE,2BAA2B;oBACnC,MAAM,EAAE,CAAC,MAAM,CAAC;iBACjB,CAAC,CAAC;gBAEH,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAE;oBACnB,MAAM,WAAW,CAAC,WAAW,CAAC,iBAAiB,CAC7C,OAAO,EACP,EAAE,CACH,CAAC;iBACH;aACF;SACF;IACH,CAAC,CAAC;AACJ,CAAC;AArED,wEAqEC"}