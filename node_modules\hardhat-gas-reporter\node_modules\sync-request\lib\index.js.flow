// @flow
// Generated using flowgen2

import type {HttpVerb} from 'then-request';
import type {Response} from 'then-request';
import {URL} from 'url';
import {FormData} from './FormData';
import type {Options} from './Options';
export type {HttpVerb};
export type {Response};
export type {Options};
export {FormData};

declare function request(
  method: HttpVerb,
  url: string | URL,
  options?: Options,
): Response;
export default request;
