"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.years = exports.weeks = exports.days = exports.hours = exports.minutes = exports.seconds = exports.millis = void 0;
var millis_1 = require("./millis");
Object.defineProperty(exports, "millis", { enumerable: true, get: function () { return millis_1.millis; } });
var seconds_1 = require("./seconds");
Object.defineProperty(exports, "seconds", { enumerable: true, get: function () { return seconds_1.seconds; } });
var minutes_1 = require("./minutes");
Object.defineProperty(exports, "minutes", { enumerable: true, get: function () { return minutes_1.minutes; } });
var hours_1 = require("./hours");
Object.defineProperty(exports, "hours", { enumerable: true, get: function () { return hours_1.hours; } });
var days_1 = require("./days");
Object.defineProperty(exports, "days", { enumerable: true, get: function () { return days_1.days; } });
var weeks_1 = require("./weeks");
Object.defineProperty(exports, "weeks", { enumerable: true, get: function () { return weeks_1.weeks; } });
var years_1 = require("./years");
Object.defineProperty(exports, "years", { enumerable: true, get: function () { return years_1.years; } });
//# sourceMappingURL=index.js.map