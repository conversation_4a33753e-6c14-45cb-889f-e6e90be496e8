const { ethers } = require("hardhat");
const readline = require("readline");

// Interactive script for contract interaction
async function main() {
  console.log("🔗 RestrictedTransfer Token Interaction Script");
  console.log("============================================\n");
  
  // Get contract address
  const contractAddress = process.env.TOKEN_CONTRACT_ADDRESS;
  if (!contractAddress) {
    console.error("❌ TOKEN_CONTRACT_ADDRESS not found in .env file");
    console.log("Please deploy the contract first or add the address to .env");
    process.exit(1);
  }
  
  // Connect to contract
  const [signer] = await ethers.getSigners();
  const token = await ethers.getContractAt("RestrictedTransferToken", contractAddress);
  
  console.log("📋 Contract Information:");
  console.log("Address:", contractAddress);
  console.log("Network:", (await ethers.provider.getNetwork()).name);
  console.log("Your Address:", signer.address);
  console.log("Your Balance:", ethers.formatEther(await ethers.provider.getBalance(signer.address)), "ETH");
  
  // Display contract details
  try {
    const name = await token.name();
    const symbol = await token.symbol();
    const totalSupply = await token.totalSupply();
    const yourBalance = await token.balanceOf(signer.address);
    const isOwner = await token.owner() === signer.address;
    
    console.log("\n🪙 Token Information:");
    console.log("Name:", name);
    console.log("Symbol:", symbol);
    console.log("Total Supply:", ethers.formatEther(totalSupply));
    console.log("Your Token Balance:", ethers.formatEther(yourBalance));
    console.log("You are owner:", isOwner ? "✅ Yes" : "❌ No");
    
  } catch (error) {
    console.error("❌ Error reading contract:", error.message);
    process.exit(1);
  }
  
  // Interactive menu
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  function showMenu() {
    console.log("\n🎛️  Available Actions:");
    console.log("1. Transfer tokens");
    console.log("2. Check balance");
    console.log("3. Check original sender");
    console.log("4. Approve spending");
    console.log("5. Check allowance");
    console.log("6. Mint tokens (owner only)");
    console.log("7. Add common token (owner only)");
    console.log("8. Set exemption (owner only)");
    console.log("9. View common tokens");
    console.log("10. Pause/Unpause contract (owner only)");
    console.log("0. Exit");
    console.log("\nEnter your choice (0-10):");
  }
  
  function askQuestion(question) {
    return new Promise((resolve) => {
      rl.question(question, resolve);
    });
  }
  
  async function handleChoice(choice) {
    try {
      switch (choice) {
        case "1":
          await handleTransfer();
          break;
        case "2":
          await handleCheckBalance();
          break;
        case "3":
          await handleCheckOriginalSender();
          break;
        case "4":
          await handleApprove();
          break;
        case "5":
          await handleCheckAllowance();
          break;
        case "6":
          await handleMint();
          break;
        case "7":
          await handleAddCommonToken();
          break;
        case "8":
          await handleSetExemption();
          break;
        case "9":
          await handleViewCommonTokens();
          break;
        case "10":
          await handlePauseUnpause();
          break;
        case "0":
          console.log("👋 Goodbye!");
          rl.close();
          return false;
        default:
          console.log("❌ Invalid choice. Please try again.");
      }
    } catch (error) {
      console.error("❌ Error:", error.message);
    }
    return true;
  }
  
  async function handleTransfer() {
    const to = await askQuestion("Enter recipient address: ");
    const amount = await askQuestion("Enter amount (in tokens): ");
    
    console.log("🔄 Sending transaction...");
    const tx = await token.transfer(to, ethers.parseEther(amount));
    console.log("📝 Transaction hash:", tx.hash);
    
    const receipt = await tx.wait();
    console.log("✅ Transaction confirmed in block:", receipt.blockNumber);
    console.log("⛽ Gas used:", receipt.gasUsed.toString());
  }
  
  async function handleCheckBalance() {
    const address = await askQuestion("Enter address to check (or press Enter for your address): ");
    const targetAddress = address || signer.address;
    
    const balance = await token.balanceOf(targetAddress);
    console.log(`💰 Balance of ${targetAddress}: ${ethers.formatEther(balance)} tokens`);
  }
  
  async function handleCheckOriginalSender() {
    const address = await askQuestion("Enter address to check: ");
    const originalSender = await token.originalSender(address);
    
    if (originalSender === ethers.ZeroAddress) {
      console.log("📭 No original sender recorded (first transfer or exempt address)");
    } else {
      console.log(`📮 Original sender: ${originalSender}`);
    }
  }
  
  async function handleApprove() {
    const spender = await askQuestion("Enter spender address: ");
    const amount = await askQuestion("Enter amount to approve: ");
    
    console.log("🔄 Sending transaction...");
    const tx = await token.approve(spender, ethers.parseEther(amount));
    await tx.wait();
    console.log("✅ Approval confirmed");
  }
  
  async function handleCheckAllowance() {
    const owner = await askQuestion("Enter owner address (or press Enter for your address): ");
    const spender = await askQuestion("Enter spender address: ");
    const ownerAddress = owner || signer.address;
    
    const allowance = await token.allowance(ownerAddress, spender);
    console.log(`🎫 Allowance: ${ethers.formatEther(allowance)} tokens`);
  }
  
  async function handleMint() {
    const to = await askQuestion("Enter recipient address: ");
    const amount = await askQuestion("Enter amount to mint: ");
    
    console.log("🔄 Sending transaction...");
    const tx = await token.mint(to, ethers.parseEther(amount));
    await tx.wait();
    console.log("✅ Tokens minted successfully");
  }
  
  async function handleAddCommonToken() {
    const tokenAddress = await askQuestion("Enter token contract address: ");
    
    console.log("🔄 Sending transaction...");
    const tx = await token.addCommonToken(tokenAddress);
    await tx.wait();
    console.log("✅ Common token added successfully");
  }
  
  async function handleSetExemption() {
    const address = await askQuestion("Enter address: ");
    const exempt = await askQuestion("Set exempt? (y/n): ");
    const isExempt = exempt.toLowerCase() === 'y' || exempt.toLowerCase() === 'yes';
    
    console.log("🔄 Sending transaction...");
    const tx = await token.setExemptFromRestrictions(address, isExempt);
    await tx.wait();
    console.log(`✅ Exemption ${isExempt ? 'granted' : 'removed'} successfully`);
  }
  
  async function handleViewCommonTokens() {
    const commonTokens = await token.getCommonTokens();
    
    if (commonTokens.length === 0) {
      console.log("📭 No common tokens configured");
    } else {
      console.log("📋 Common tokens for recovery:");
      commonTokens.forEach((tokenAddr, index) => {
        console.log(`${index + 1}. ${tokenAddr}`);
      });
    }
  }
  
  async function handlePauseUnpause() {
    const isPaused = await token.paused();
    const action = isPaused ? "unpause" : "pause";
    
    const confirm = await askQuestion(`Contract is currently ${isPaused ? 'paused' : 'active'}. ${action}? (y/n): `);
    if (confirm.toLowerCase() === 'y' || confirm.toLowerCase() === 'yes') {
      console.log("🔄 Sending transaction...");
      const tx = isPaused ? await token.unpause() : await token.pause();
      await tx.wait();
      console.log(`✅ Contract ${action}d successfully`);
    }
  }
  
  // Main interaction loop
  let continueLoop = true;
  while (continueLoop) {
    showMenu();
    const choice = await askQuestion("");
    continueLoop = await handleChoice(choice);
  }
}

// Handle errors and cleanup
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
