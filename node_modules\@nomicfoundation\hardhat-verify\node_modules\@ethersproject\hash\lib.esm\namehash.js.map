{"version": 3, "file": "namehash.js", "sourceRoot": "", "sources": ["../src.ts/namehash.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AACnE,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AAErD,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC;AAEnC,OAAO,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AAEpD,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;AACjC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAEd,SAAS,cAAc,CAAC,IAAgB;IACpC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QAAE,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;KAAE;IAChF,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,SAAS,YAAY,CAAC,IAAY;IAC9B,MAAM,KAAK,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IAC/C,MAAM,KAAK,GAAsB,EAAG,CAAC;IAErC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QAAE,OAAO,KAAK,CAAC;KAAE;IAExC,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEnB,8CAA8C;QAC9C,IAAI,CAAC,KAAK,IAAI,EAAE;YACZ,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;SAChB;KACJ;IAED,qDAAqD;IACrD,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE;QAAE,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;KAAE;IAEnF,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC9C,OAAO,KAAK,CAAC;AACjB,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,IAAY;IACrC,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1E,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,IAAY;IACpC,IAAI;QACA,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;KAC5C;IAAC,OAAO,KAAK,EAAE,GAAG;IACnB,OAAO,KAAK,CAAC;AACjB,CAAC;AAED,MAAM,UAAU,QAAQ,CAAC,IAAY;IACjC,wBAAwB;IACxB,IAAI,OAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE;QAC3B,MAAM,CAAC,kBAAkB,CAAC,gCAAgC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;KAC7E;IAED,IAAI,MAAM,GAAwB,KAAK,CAAC;IAExC,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IACjC,OAAO,KAAK,CAAC,MAAM,EAAE;QACjB,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;KAChE;IAED,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC;AAC3B,CAAC;AAED,MAAM,UAAU,SAAS,CAAC,IAAY;IAClC,OAAO,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QAClD,wDAAwD;QACxD,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;SACzE;QAED,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC9C,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACnB,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAC5B,OAAO,KAAK,CAAC;IAEjB,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAChB,CAAC"}