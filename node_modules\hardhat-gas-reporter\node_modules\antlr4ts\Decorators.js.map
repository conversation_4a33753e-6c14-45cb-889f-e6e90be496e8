{"version": 3, "file": "Decorators.js", "sourceRoot": "", "sources": ["../../src/Decorators.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,SAAgB,OAAO,CACtB,MAAW,EACX,WAAwB,EACxB,kBAAgD;IAChD,sBAAsB;AACvB,CAAC;AALD,0BAKC;AAED,SAAgB,QAAQ,CACvB,MAAW,EACX,WAAwB,EACxB,kBAAgD;IAChD,sBAAsB;AACvB,CAAC;AALD,4BAKC;AAED,SAAgB,QAAQ,CACvB,MAAW,EACX,WAAwB,EACxB,kBAAuC;IACvC,iCAAiC;AAClC,CAAC;AALD,4BAKC;AAED,SAAgB,gBAAgB,CAAC,OAAe;IAC/C,OAAO,CAAC,MAAW,EAAE,WAAwB,EAAE,UAA+B,EAAE,EAAE;QACjF,sBAAsB;IACvB,CAAC,CAAC;AACH,CAAC;AAJD,4CAIC", "sourcesContent": ["/*!\r\n * Copyright 2016 The ANTLR Project. All rights reserved.\r\n * Licensed under the BSD-3-Clause license. See LICENSE file in the project root for license information.\r\n */\r\n\r\nexport function NotNull(\r\n\ttarget: any,\r\n\tpropertyKey: PropertyKey,\r\n\tpropertyDescriptor?: PropertyDescriptor | number) {\r\n\t// intentionally empty\r\n}\r\n\r\nexport function Nullable(\r\n\ttarget: any,\r\n\tpropertyKey: PropertyKey,\r\n\tpropertyDescriptor?: PropertyDescriptor | number) {\r\n\t// intentionally empty\r\n}\r\n\r\nexport function Override(\r\n\ttarget: any,\r\n\tpropertyKey: PropertyKey,\r\n\tpropertyDescriptor?: PropertyDescriptor) {\r\n\t// do something with 'target' ...\r\n}\r\n\r\nexport function SuppressWarnings(options: string) {\r\n\treturn (target: any, propertyKey: PropertyKey, descriptor?: PropertyDescriptor) => {\r\n\t\t// intentionally empty\r\n\t};\r\n}\r\n"]}