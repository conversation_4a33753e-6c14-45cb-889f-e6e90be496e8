{"version": 3, "file": "events.js", "sourceRoot": "", "sources": ["../../src/codegen/events.ts"], "names": [], "mappings": ";;;AAAA,sDAAsD;AACtD,yCAKkB;AAElB,mCAKgB;AAEhB,SAAgB,oBAAoB,CAAC,MAA0B;IAC7D,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACvB,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAA;QACvB,MAAM,gBAAgB,GAAG,0BAA0B,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QACjE,OAAO;SACF,sBAAsB,CAAC,KAAK,CAAC,MAAM,gBAAgB;QACpD,KAAK,CAAC,IAAI,KAAK,gBAAgB;KAClC,CAAA;KACF;SAAM;QACL,OAAO,MAAM;aACV,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,sBAAsB,CAAC,KAAK,CAAC,MAAM,0BAA0B,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC;aACjG,IAAI,CAAC,IAAI,CAAC,CAAA;KACd;AACH,CAAC;AAbD,oDAaC;AAED,SAAgB,wBAAwB,CAAC,MAA0B;IACjE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO,uBAAuB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;KACjD;SAAM;QACL,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;KACtE;AACH,CAAC;AAND,4DAMC;AAED,SAAgB,uBAAuB,CAAC,KAAuB,EAAE,eAAwB;IACvF,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,WAAC,OAAA,CAAC,EAAE,IAAI,EAAE,MAAA,KAAK,CAAC,IAAI,mCAAI,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAA,EAAA,CAAC,CAAA;IACnH,MAAM,UAAU,GAAG,IAAA,uCAA+B,EAAC,UAAU,EAAE;QAC7D,UAAU,EAAE,IAAI;QAChB,yBAAyB,EAAE,IAAI;KAChC,CAAC,CAAA;IACF,MAAM,WAAW,GAAG,IAAA,wCAAgC,EAAC,UAAU,EAAE;QAC/D,UAAU,EAAE,IAAI;QAChB,yBAAyB,EAAE,IAAI;KAChC,CAAC,CAAA;IACF,MAAM,YAAY,GAAG,IAAA,0CAAkC,EAAC,UAAU,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,IAAI,IAAI,CAAA;IAEjG,MAAM,UAAU,GAAG,uBAAuB,CAAC,KAAK,EAAE,EAAE,eAAe,EAAE,CAAC,CAAA;IAEtE,OAAO;uBACc,UAAU;iCACA,UAAU;kCACT,WAAW;sCACP,YAAY;;;;;;;GAO/C,CAAA;AACH,CAAC;AA1BD,0DA0BC;AAED,SAAgB,iCAAiC,CAAC,KAAuB;IACvE,OAAO,IAAI,sBAAsB,CAAC,KAAK,CAAC,mBAAmB,CAAA;AAC7D,CAAC;AAFD,8EAEC;AAED,SAAgB,sBAAsB,CAAC,KAAuB;IAC5D,OAAO,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAA;AAChG,CAAC;AAFD,wDAEC;AAED,SAAgB,mBAAmB,CAAC,SAAgC;IAClE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;QAC1B,OAAO,EAAE,CAAA;KACV;IACD,OAAO,CACL,SAAS;SACN,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;QAClB,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,sCAA0B,EAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,EAAE,MAAM,oBAAoB,CAAC,GAAG,CAAC,EAAE,CAAA;IAC5G,CAAC,CAAC;SACD,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CACrB,CAAA;AACH,CAAC;AAXD,kDAWC;AAED,SAAgB,oBAAoB,CAAC,QAA6B;IAChE,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAA,yBAAiB,EAAC,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAA;AACzG,CAAC;AAFD,oDAEC;AAED,SAAgB,4BAA4B,CAAC,KAAuB,EAAE,YAAqB;IACzF,OAAO,YAAY,CAAC,CAAC,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAA;AAClE,CAAC;AAFD,oEAEC;AAED,yGAAyG;AACzG,iDAAiD;AACjD,gEAAgE;AAChE,yBAAyB;AACzB,IAAI;AAEJ,SAAgB,4BAA4B,CAAC,IAAc;IACzD,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,EAAE,CAAA;IAEhC,OAAO,oCAAoC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAA;AACrG,CAAC;AAJD,oEAIC;AAED,SAAgB,0BAA0B,CAAC,KAAuB,EAAE,YAAqB;IACvF,MAAM,eAAe,GAAG,uBAAuB,CAAC,KAAK,EAAE;QACrD,eAAe,EAAE,YAAY;KAC9B,CAAC,CAAA;IACF,OAAO,sBAAsB,eAAe,gBAAgB,eAAe,iBAAiB,eAAe,gBAAgB,CAAA;AAC7H,CAAC;AALD,gEAKC;AAED,SAAgB,2BAA2B,CAAC,KAAuB,EAAE,YAAqB;IACxF,MAAM,kBAAkB,GAAG,0BAA0B,CAAC,KAAK,EAAE,YAAY,CAAC,CAAA;IAC1E,OAAO,kBAAkB,YAAY,CAAC,CAAC,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,OAAO,kBAAkB,GAAG,CAAA;AAChH,CAAC;AAHD,kEAGC;AAED,SAAS,uBAAuB,CAAC,KAAuB,EAAE,EAAE,eAAe,KAAoC,EAAE;IAC/G,IAAI,eAAe,EAAE;QACnB,OAAO,IAAA,4CAAgC,EAAC,KAAK,CAAC,GAAG,QAAQ,CAAA;KAC1D;SAAM;QACL,OAAO,KAAK,CAAC,IAAI,GAAG,OAAO,CAAA;KAC5B;AACH,CAAC;AAEY,QAAA,sBAAsB,GAAG;;;;;;;;;;;;;;;;;;;;;;;CAuBrC,CAAA;AAEY,QAAA,aAAa,GAAG;IAC3B,oBAAoB;IACpB,0BAA0B;IAC1B,eAAe;IACf,qBAAqB;IACrB,eAAe;CAChB,CAAA"}