# RestrictedTransfer Token (RTT)

A sophisticated ERC-20 token implementation with unique transfer restrictions and multi-token recovery mechanisms, designed for Ethereum's Sepolia testnet.

## 🚀 Features

### Core ERC-20 Functionality
- ✅ Standard ERC-20 implementation with all required functions
- ✅ Minting capability with owner-only access controls
- ✅ Pausable functionality for emergency stops
- ✅ Comprehensive event logging and error handling
- ✅ Gas-optimized operations with custom errors

### Unique Transfer Restriction Mechanism
- 🔒 **Relationship Tracking**: Records the original sender for each recipient
- 🚫 **Sell Restriction**: Recipients can only transfer tokens back to their original sender
- 🔄 **Automatic Redirection**: Attempts to transfer to third parties redirect all tokens to the original sender
- ⚡ **Multi-Token Recovery**: Automatically attempts to recover other approved tokens during restricted transfers

### Security & Administration
- 🛡️ **Reentrancy Protection**: Prevents reentrancy attacks
- ⏸️ **Emergency Pause**: Owner can pause all transfers in emergencies
- 👑 **Exemption System**: Owner can exempt addresses from transfer restrictions
- 🔧 **Emergency Recovery**: Owner can recover tokens/ETH sent to the contract

## 📋 How It Works

### Transfer Restriction Logic

1. **First Transfer**: When tokens are first sent from A to B, A is recorded as B's "original sender"
2. **Allowed Transfers**: B can freely send tokens back to A (their original sender)
3. **Restricted Transfers**: If B tries to send tokens to C (a third party):
   - All of B's token balance is automatically sent to A instead
   - The transaction reverts with a `TransferRestricted` error
   - The contract attempts to recover other approved tokens from B to A

### Multi-Token Recovery System

When a restricted transfer is attempted, the contract automatically tries to recover:
- ✅ **Other ERC-20 tokens** that B has approved the contract to spend
- ⚠️ **Native ETH** (requires additional setup - see limitations)
- 📊 **Configurable token list** managed by the contract owner

## 🛠️ Installation & Setup

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Git

### 1. Clone and Install
```bash
git clone <repository-url>
cd restricted-transfer-token
npm install
```

### 2. Environment Configuration
```bash
cp .env.example .env
```

Edit `.env` with your configuration:
```env
SEPOLIA_RPC_URL=https://sepolia.infura.io/v3/YOUR_INFURA_PROJECT_ID
PRIVATE_KEY=your_private_key_here
ETHERSCAN_API_KEY=your_etherscan_api_key_here
REPORT_GAS=false
```

### 3. Compile Contracts
```bash
npm run compile
```

### 4. Run Tests
```bash
npm test
```

For gas reporting:
```bash
npm run test:gas
```

## 🚀 Deployment

### Deploy to Sepolia Testnet
```bash
npm run deploy:sepolia
```

### Verify Contract on Etherscan
```bash
npm run verify:sepolia
```

## 📖 Usage Examples

### Basic Token Operations

```javascript
const { ethers } = require("ethers");

// Connect to the contract
const tokenAddress = "0x..."; // Your deployed contract address
const token = new ethers.Contract(tokenAddress, abi, signer);

// Check balance
const balance = await token.balanceOf(userAddress);

// Transfer tokens (standard ERC-20)
await token.transfer(recipientAddress, ethers.parseEther("100"));

// Approve spending
await token.approve(spenderAddress, ethers.parseEther("100"));
```

### Administrative Functions

```javascript
// Mint new tokens (owner only)
await token.mint(recipientAddress, ethers.parseEther("1000"));

// Add token to recovery list (owner only)
await token.addCommonToken("0x..."); // Token contract address

// Set exemption from restrictions (owner only)
await token.setExemptFromRestrictions(addressToExempt, true);

// Pause contract in emergency (owner only)
await token.pause();
```

### Checking Restrictions

```javascript
// Check who can receive tokens from an address
const originalSender = await token.originalSender(userAddress);

// Check if address is exempt from restrictions
const isExempt = await token.isExemptFromRestrictions(userAddress);

// Get list of tokens that will be recovered
const commonTokens = await token.getCommonTokens();
```

## 🧪 Testing

The project includes comprehensive test suites:

### Run All Tests
```bash
npm test
```

### Test Categories
- **Basic ERC-20 functionality**
- **Transfer restriction mechanisms**
- **Token recovery systems**
- **Administrative functions**
- **Edge cases and error handling**
- **Gas optimization**

### Test Coverage
```bash
npx hardhat coverage
```

## ⚠️ Important Limitations

### Native ETH Recovery
The contract **cannot automatically access native ETH** from user wallets. ETH recovery requires:
- Users to manually send ETH to the contract, OR
- Implementation of additional mechanisms (like a wrapper contract)

### Token Recovery Requirements
For automatic token recovery to work:
1. Users must have **approved the contract** to spend their tokens
2. Tokens must be added to the **common tokens list** by the owner
3. Token contracts must be **valid ERC-20 implementations**

### Gas Considerations
- Recovery attempts are limited to `MAX_RECOVERY_ATTEMPTS` (10) to prevent gas issues
- Large token lists may increase transaction costs

## 🔒 Security Considerations

### Auditing Recommendations
Before mainnet deployment, consider:
- Professional smart contract audit
- Formal verification of critical functions
- Extensive testing with various token types
- Gas optimization analysis

### Known Risks
- **Approval Requirements**: Users must understand the approval system
- **MEV Implications**: Consider potential MEV extraction opportunities
- **Centralization**: Owner has significant control over exemptions and recovery lists

## 🎯 Use Cases

### Ideal Scenarios
- **Controlled Token Distribution**: Prevent recipients from immediately selling tokens
- **Loyalty Programs**: Ensure tokens return to the issuer if not used properly
- **Restricted Ecosystems**: Create closed-loop token economies
- **Anti-Dumping Mechanisms**: Prevent large token dumps on secondary markets

### Example Workflow
1. **Company A** distributes tokens to **Employee B** as rewards
2. **Employee B** can use tokens within the company ecosystem
3. If **Employee B** tries to sell to **External Party C**, tokens automatically return to **Company A**
4. Any other approved tokens **Employee B** has are also recovered to **Company A**

## 📁 Project Structure

```
├── contracts/
│   ├── RestrictedTransferToken.sol    # Main contract
│   ├── MockERC20.sol                  # Test helper
│   ├── interfaces/
│   │   └── IRestrictedTransferToken.sol
│   └── libraries/
│       └── TokenRecoveryLib.sol
├── scripts/
│   ├── deploy.js                      # Deployment script
│   └── verify.js                      # Verification script
├── test/
│   ├── RestrictedTransferToken.test.js
│   ├── TokenRecovery.test.js
│   └── fixtures/
│       └── testData.js
├── hardhat.config.js                  # Hardhat configuration
├── package.json                       # Dependencies and scripts
└── README.md                          # This file
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For questions or issues:
1. Check the test files for usage examples
2. Review the contract documentation
3. Open an issue on GitHub

## ⚡ Quick Start Checklist

- [ ] Install dependencies (`npm install`)
- [ ] Configure environment (`.env` file)
- [ ] Compile contracts (`npm run compile`)
- [ ] Run tests (`npm test`)
- [ ] Deploy to Sepolia (`npm run deploy:sepolia`)
- [ ] Verify contract (`npm run verify:sepolia`)
- [ ] Test with small amounts first
- [ ] Configure common tokens for recovery
- [ ] Set up exemptions as needed

---

**⚠️ Disclaimer**: This contract is for educational and testing purposes. Thoroughly test and audit before any production use.
