{"version": 3, "file": "bignumber.d.ts", "sourceRoot": "", "sources": ["../src.ts/bignumber.ts"], "names": [], "mappings": "AAaA,OAAO,EAAE,KAAK,EAAE,OAAO,EAAiC,MAAM,sBAAsB,CAAC;AAWrF,oBAAY,YAAY,GAAG,SAAS,GAAG,KAAK,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AAExE,wBAAgB,cAAc,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,IAAI,YAAY,CAShE;AAKD,qBAAa,SAAU,YAAW,OAAO;IACrC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,YAAY,EAAE,OAAO,CAAC;gBAEnB,gBAAgB,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM;IAa9C,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,SAAS;IAIlC,MAAM,CAAC,KAAK,EAAE,MAAM,GAAG,SAAS;IAIhC,GAAG,IAAI,SAAS;IAOhB,GAAG,CAAC,KAAK,EAAE,YAAY,GAAG,SAAS;IAInC,GAAG,CAAC,KAAK,EAAE,YAAY,GAAG,SAAS;IAInC,GAAG,CAAC,KAAK,EAAE,YAAY,GAAG,SAAS;IAQnC,GAAG,CAAC,KAAK,EAAE,YAAY,GAAG,SAAS;IAInC,GAAG,CAAC,KAAK,EAAE,YAAY,GAAG,SAAS;IAQnC,GAAG,CAAC,KAAK,EAAE,YAAY,GAAG,SAAS;IAQnC,GAAG,CAAC,KAAK,EAAE,YAAY,GAAG,SAAS;IAQnC,EAAE,CAAC,KAAK,EAAE,YAAY,GAAG,SAAS;IAQlC,GAAG,CAAC,KAAK,EAAE,YAAY,GAAG,SAAS;IAQnC,IAAI,CAAC,KAAK,EAAE,MAAM,GAAG,SAAS;IAO9B,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,SAAS;IAO7B,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,SAAS;IAO7B,EAAE,CAAC,KAAK,EAAE,YAAY,GAAG,OAAO;IAIhC,EAAE,CAAC,KAAK,EAAE,YAAY,GAAG,OAAO;IAIhC,GAAG,CAAC,KAAK,EAAE,YAAY,GAAG,OAAO;IAIjC,EAAE,CAAC,KAAK,EAAE,YAAY,GAAG,OAAO;IAIhC,GAAG,CAAC,KAAK,EAAE,YAAY,GAAG,OAAO;IAIjC,UAAU,IAAI,OAAO;IAIrB,MAAM,IAAI,OAAO;IAIjB,QAAQ,IAAI,MAAM;IASlB,QAAQ,IAAI,MAAM;IAUlB,QAAQ,IAAI,MAAM;IAiBlB,WAAW,IAAI,MAAM;IAIrB,MAAM,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,GAAG;IAIzB,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,GAAG,SAAS;IAkElC,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,IAAI,SAAS;CAGrD;AAiED,wBAAgB,WAAW,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,CAEjD;AAGD,wBAAgB,WAAW,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,CAEjD"}