const { ethers } = require("hardhat");

// Common test data and utilities
const TEST_DATA = {
  TOKEN_NAME: "RestrictedTransfer Token",
  TOKEN_SYMBOL: "RTT",
  INITIAL_SUPPLY: ethers.parseEther("1000000"),
  
  // Test amounts
  AMOUNTS: {
    SMALL: ethers.parseEther("10"),
    MEDIUM: ethers.parseEther("100"),
    LARGE: ethers.parseEther("1000"),
  },
  
  // Mock token data
  MOCK_TOKENS: [
    { name: "Mock USDC", symbol: "MUSDC", supply: ethers.parseEther("1000000") },
    { name: "Mock USDT", symbol: "MUSDT", supply: ethers.parseEther("1000000") },
    { name: "Mock DAI", symbol: "MDAI", supply: ethers.parseEther("1000000") },
  ],
  
  // Common addresses for testing (these are deterministic test addresses)
  ADDRESSES: {
    ZERO: ethers.ZeroAddress,
    DEAD: "******************************************",
    FAKE_TOKEN: "******************************************",
  }
};

// Utility functions for tests
const TEST_UTILS = {
  // Generate random address
  randomAddress: () => ethers.Wallet.createRandom().address,
  
  // Format ether for display
  formatEther: (amount) => ethers.formatEther(amount),
  
  // Parse ether from string
  parseEther: (amount) => ethers.parseEther(amount.toString()),
  
  // Get current timestamp
  getCurrentTimestamp: async () => {
    const block = await ethers.provider.getBlock("latest");
    return block.timestamp;
  },
  
  // Increase time in hardhat network
  increaseTime: async (seconds) => {
    await ethers.provider.send("evm_increaseTime", [seconds]);
    await ethers.provider.send("evm_mine");
  },
  
  // Get gas used from transaction
  getGasUsed: async (tx) => {
    const receipt = await tx.wait();
    return receipt.gasUsed;
  },
  
  // Check if address is contract
  isContract: async (address) => {
    const code = await ethers.provider.getCode(address);
    return code !== "0x";
  }
};

// Common deployment fixtures
const DEPLOYMENT_FIXTURES = {
  // Deploy main token with default parameters
  deployMainToken: async (owner) => {
    const RestrictedTransferToken = await ethers.getContractFactory("RestrictedTransferToken");
    return await RestrictedTransferToken.deploy(
      TEST_DATA.TOKEN_NAME,
      TEST_DATA.TOKEN_SYMBOL,
      TEST_DATA.INITIAL_SUPPLY,
      owner.address
    );
  },
  
  // Deploy mock token
  deployMockToken: async (tokenData) => {
    const MockERC20 = await ethers.getContractFactory("MockERC20");
    return await MockERC20.deploy(
      tokenData.name,
      tokenData.symbol,
      tokenData.supply
    );
  },
  
  // Deploy multiple mock tokens
  deployMockTokens: async () => {
    const tokens = [];
    for (const tokenData of TEST_DATA.MOCK_TOKENS) {
      const token = await DEPLOYMENT_FIXTURES.deployMockToken(tokenData);
      tokens.push(token);
    }
    return tokens;
  }
};

// Test scenarios for comprehensive testing
const TEST_SCENARIOS = {
  // Basic transfer scenarios
  BASIC_TRANSFERS: [
    {
      name: "Owner to User",
      description: "Owner transfers tokens to a user (should be allowed)",
      fromExempt: true,
      shouldSucceed: true
    },
    {
      name: "User to Original Sender",
      description: "User transfers back to original sender (should be allowed)",
      toOriginalSender: true,
      shouldSucceed: true
    },
    {
      name: "User to Third Party",
      description: "User transfers to third party (should be restricted)",
      toThirdParty: true,
      shouldSucceed: false
    }
  ],
  
  // Edge cases
  EDGE_CASES: [
    {
      name: "Zero Amount Transfer",
      amount: 0,
      shouldRevert: true,
      expectedError: "ZeroAmount"
    },
    {
      name: "Zero Address Transfer",
      toZeroAddress: true,
      shouldRevert: true,
      expectedError: "ZeroAddress"
    },
    {
      name: "Self Transfer",
      selfTransfer: true,
      shouldSucceed: true
    },
    {
      name: "Insufficient Balance",
      insufficientBalance: true,
      shouldRevert: true,
      expectedError: "ERC20InsufficientBalance"
    }
  ],
  
  // Recovery scenarios
  RECOVERY_SCENARIOS: [
    {
      name: "Single Token Recovery",
      tokensToRecover: 1,
      approveTokens: true,
      expectedSuccess: true
    },
    {
      name: "Multiple Token Recovery",
      tokensToRecover: 3,
      approveTokens: true,
      expectedSuccess: true
    },
    {
      name: "No Approval Recovery",
      tokensToRecover: 1,
      approveTokens: false,
      expectedSuccess: false
    },
    {
      name: "Mixed Approval Recovery",
      tokensToRecover: 3,
      partialApproval: true,
      expectedSuccess: "mixed"
    }
  ]
};

module.exports = {
  TEST_DATA,
  TEST_UTILS,
  DEPLOYMENT_FIXTURES,
  TEST_SCENARIOS
};
