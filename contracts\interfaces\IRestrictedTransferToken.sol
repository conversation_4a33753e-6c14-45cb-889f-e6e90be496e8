// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

/**
 * @title IRestrictedTransferToken
 * @dev Interface for the RestrictedTransferToken contract
 */
interface IRestrictedTransferToken is IERC20 {
    
    // Custom errors
    error TransferRestricted(address from, address to, address allowedRecipient);
    error ZeroAddress();
    error ZeroAmount();
    error InsufficientBalance();
    error RecoveryFailed(address token, string reason);
    
    // Events
    event OriginalSenderRecorded(address indexed recipient, address indexed originalSender);
    event RestrictedTransferAttempted(address indexed from, address indexed intendedTo, address indexed redirectedTo, uint256 amount);
    event TokenRecoveryAttempted(address indexed from, address indexed to, address indexed token, uint256 amount, bool success);
    event EthRecoveryAttempted(address indexed from, address indexed to, uint256 amount, bool success);
    event TokenMinted(address indexed to, uint256 amount);
    
    // View functions
    function originalSender(address recipient) external view returns (address);
    function isExemptFromRestrictions(address account) external view returns (bool);
    function getCommonTokens() external view returns (address[] memory);
    
    // Admin functions
    function mint(address to, uint256 amount) external;
    function setExemptFromRestrictions(address account, bool exempt) external;
    function addCommonToken(address tokenAddress) external;
    function removeCommonToken(address tokenAddress) external;
    function pause() external;
    function unpause() external;
    function emergencyRecoverToken(address tokenAddress, address to, uint256 amount) external;
    function emergencyRecoverEth(address payable to, uint256 amount) external;
}
