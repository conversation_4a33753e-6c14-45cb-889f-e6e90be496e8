{"version": 3, "file": "bigNumber.js", "sourceRoot": "", "sources": ["../src/internal/bigNumber.ts"], "names": [], "mappings": ";;;;;;AAAA,+BAAsC;AACtC,kDAAuE;AACvE,gDAAwB;AAExB,SAAgB,gBAAgB,CAC9B,SAA+B,EAC/B,SAAyB;IAEzB,MAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;IACvE,SAAS,CAAC,eAAe,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;IACpD,SAAS,CAAC,eAAe,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;IACnD,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IAEhD,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;IACvE,SAAS,CAAC,eAAe,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC/C,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IAC5C,SAAS,CAAC,eAAe,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;IAErD,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC;IACxE,SAAS,CAAC,eAAe,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC/C,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IAC5C,SAAS,CAAC,eAAe,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IAElD,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,EAAE,aAAa,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;IAC1E,SAAS,CAAC,eAAe,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;IAChD,SAAS,CAAC,eAAe,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;IAC9C,SAAS,CAAC,eAAe,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAAC;IAE7D,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;IACzE,SAAS,CAAC,eAAe,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IAC/C,SAAS,CAAC,eAAe,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;IAC9C,SAAS,CAAC,eAAe,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;IAE1D,SAAS,CAAC,wBAAwB,CAAC,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC;IACtE,SAAS,CAAC,wBAAwB,CAAC,GAAG,oBAAoB,CAAC,UAAU,CAAC,CAAC,CAAC;IAExE,SAAS,CAAC,eAAe,CAAC,QAAQ,EAAE,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC;IAE/D,SAAS,CAAC,eAAe,CAAC,SAAS,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC;IACjE,SAAS,CAAC,eAAe,CAAC,eAAe,EAAE,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC;AACzE,CAAC;AApCD,4CAoCC;AAED,SAAS,oBAAoB,CAC3B,MAAc;IAEd,OAAO;QACL,MAAM;QACN,UAAU,MAAW;YACnB,OAAO,UAAyC,KAAU;gBACxD,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;gBACzB,IAAI,IAAA,oBAAW,EAAC,KAAK,CAAC,EAAE;oBACtB,MAAM,YAAY,GAChB,MAAM,YAAY,GAAG,IAAI,MAAM,YAAY,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC;oBACrE,MAAM,YAAY,GAAG,IAAA,0BAAiB,EAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;oBAC7D,MAAM,cAAc,GAAG,IAAA,0BAAiB,EAAC,KAAK,CAAC,CAAC;oBAChD,IAAI,CAAC,MAAM,CACT,YAAY,KAAK,cAAc,EAC/B,8BAA8B,YAAY,OAAO,cAAc,CAAC,QAAQ,EAAE,YAAY,YAAY,CAAC,QAAQ,EAAE,EAAE,EAC/G,kCAAkC,YAAY,OAAO,cAAc,CAAC,QAAQ,EAAE,YAAY,YAAY,CAAC,QAAQ,EAAE,EAAE,EACnH,YAAY,CAAC,QAAQ,EAAE,EACvB,cAAc,CAAC,QAAQ,EAAE,CAC1B,CAAC;iBACH;qBAAM;oBACL,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;iBAC/B;YACH,CAAC,CAAC;QACJ,CAAC;QACD,UAAU,MAAW;YACnB,OAAO;gBACL,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAChC,CAAC,CAAC;QACJ,CAAQ;KACT,CAAC;AACJ,CAAC;AAID,SAAS,QAAQ,CACf,MAAe,EACf,IAAY,EACZ,YAAoB,EACpB,SAAyB;IAEzB,OAAO,CAAC,MAA+B,EAAE,EAAE,CACzC,0BAA0B,CAAC,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;AAC9E,CAAC;AAED,SAAS,0BAA0B,CACjC,YAAqB,EACrB,YAAoB,EACpB,oBAA4B,EAC5B,MAA+B,EAC/B,SAAyB;IAEzB,OAAO,UAAsC,GAAG,IAAW;QACzD,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC;QAClC,MAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QAEpD,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;SAC1C;QAED,SAAS,OAAO,CAAC,MAAe,EAAE,GAAW,EAAE,GAAW;YACxD,IAAI,MAAM,KAAK,IAAI,EAAE;gBACnB,OAAO,GAAG,KAAK,GAAG,CAAC;aACpB;iBAAM,IAAI,MAAM,KAAK,IAAI,EAAE;gBAC1B,OAAO,GAAG,GAAG,GAAG,CAAC;aAClB;iBAAM,IAAI,MAAM,KAAK,IAAI,EAAE;gBAC1B,OAAO,GAAG,GAAG,GAAG,CAAC;aAClB;iBAAM,IAAI,MAAM,KAAK,KAAK,EAAE;gBAC3B,OAAO,GAAG,IAAI,GAAG,CAAC;aACnB;iBAAM,IAAI,MAAM,KAAK,KAAK,EAAE;gBAC3B,OAAO,GAAG,IAAI,GAAG,CAAC;aACnB;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,gCAAgC,MAAa,EAAE,CAAC,CAAC;aAClE;QACH,CAAC;QACD,IAAI,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,IAAI,IAAA,oBAAW,EAAC,SAAS,CAAC,EAAE;YACvE,MAAM,YAAY,GAChB,YAAY,YAAY,GAAG,IAAI,YAAY,YAAY,GAAG;gBACxD,CAAC,CAAC,MAAM;gBACR,CAAC,CAAC,QAAQ,CAAC;YACf,IAAI,YAAY,CAAC,YAAY,CAAC,KAAK,SAAS,EAAE;gBAC5C,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACzB,OAAO;aACR;YACD,MAAM,QAAQ,GAAG,IAAA,0BAAiB,EAAC,YAAY,CAAC,YAAY,CAAC,CAAC,CAAC;YAC/D,MAAM,MAAM,GAAG,IAAA,0BAAiB,EAAC,SAAS,CAAC,CAAC;YAC5C,IAAI,CAAC,MAAM,CACT,OAAO,CAAC,YAAY,EAAE,QAAQ,EAAE,MAAM,CAAC,EACvC,8BAA8B,YAAY,IAAI,YAAY,CAAC,OAAO,CAChE,KAAK,EACL,EAAE,CACH,IAAI,MAAM,CAAC,QAAQ,EAAE,YAAY,QAAQ,EAAE,EAC5C,8BAA8B,YAAY,IAAI,oBAAoB,IAAI,MAAM,CAAC,QAAQ,EAAE,EAAE,EACzF,QAAQ,EACR,MAAM,CACP,CAAC;SACH;aAAM,IAAI,YAAY,KAAK,IAAI,IAAI,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,EAAE;YACzE,MAAM,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;YACtC,mEAAmE;YACnE,iCAAiC;YACjC,MAAM,UAAU,GAAG,CAAC,CAAM,EAAE,CAAM,EAAkB,EAAE;gBACpD,IAAI;oBACF,MAAM,WAAW,GAAG,IAAA,0BAAiB,EAAC,CAAC,CAAC,CAAC;oBACzC,MAAM,WAAW,GAAG,IAAA,0BAAiB,EAAC,CAAC,CAAC,CAAC;oBACzC,OAAO,WAAW,KAAK,WAAW,CAAC;iBACpC;gBAAC,OAAO,CAAC,EAAE;oBACV,yBAAyB;oBACzB,OAAO,IAAI,CAAC;iBACb;YACH,CAAC,CAAC;YAEF,0EAA0E;YAC1E,+DAA+D;YAC/D,wEAAwE;YACxE,MAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;YACtD,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;YACvC,IAAI,CAAC,MAAM,CACT,SAAS,CAAC,SAAS,EAAE,YAAY,EAAE,EAAE,UAAU,EAAE,CAAC,EAClD,YAAY,cAAI,CAAC,OAAO,CAAC,YAAY,CAAC,oBAAoB,cAAI,CAAC,OAAO,CACpE,SAAS,CACV,EAAE,EACH,YAAY,cAAI,CAAC,OAAO,CACtB,YAAY,CACb,wBAAwB,cAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,EAClD,IAAI,CACL,CAAC;YACF,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,YAAY,CAAC,CAAC;SAChD;aAAM,IAAI,IAAA,oBAAW,EAAC,YAAY,CAAC,IAAI,IAAA,oBAAW,EAAC,SAAS,CAAC,EAAE;YAC9D,MAAM,QAAQ,GAAG,IAAA,0BAAiB,EAAC,YAAY,CAAC,CAAC;YACjD,MAAM,MAAM,GAAG,IAAA,0BAAiB,EAAC,SAAS,CAAC,CAAC;YAC5C,IAAI,CAAC,MAAM,CACT,OAAO,CAAC,YAAY,EAAE,QAAQ,EAAE,MAAM,CAAC,EACvC,YAAY,QAAQ,OAAO,YAAY,IAAI,MAAM,GAAG,EACpD,YAAY,QAAQ,OAAO,oBAAoB,IAAI,MAAM,GAAG,EAC5D,MAAM,CAAC,QAAQ,EAAE,EACjB,QAAQ,CAAC,QAAQ,EAAE,CACpB,CAAC;SACH;aAAM;YACL,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAC1B;IACH,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,cAAc,CAAC,SAAyB;IAC/C,OAAO,CAAC,MAA+B,EAAE,EAAE,CACzC,wBAAwB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAChD,CAAC;AAED,SAAS,wBAAwB,CAC/B,MAA+B,EAC/B,SAAyB;IAEzB,OAAO,UAAsC,GAAG,IAAW;QACzD,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;QACnC,MAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACpD,IACE,IAAA,oBAAW,EAAC,YAAY,CAAC;YACzB,IAAA,oBAAW,EAAC,QAAQ,CAAC;YACrB,IAAA,oBAAW,EAAC,SAAS,CAAC,EACtB;YACA,MAAM,QAAQ,GAAG,IAAA,0BAAiB,EAAC,YAAY,CAAC,CAAC;YACjD,MAAM,KAAK,GAAG,IAAA,0BAAiB,EAAC,QAAQ,CAAC,CAAC;YAC1C,MAAM,MAAM,GAAG,IAAA,0BAAiB,EAAC,SAAS,CAAC,CAAC;YAC5C,IAAI,CAAC,MAAM,CACT,KAAK,IAAI,QAAQ,IAAI,QAAQ,IAAI,MAAM,EACvC,YAAY,QAAQ,iBAAiB,KAAK,KAAK,MAAM,EAAE,EACvD,YAAY,QAAQ,qBAAqB,KAAK,KAAK,MAAM,EAAE,EAC3D,QAAQ,EACR,CAAC,KAAK,EAAE,MAAM,CAAC,CAChB,CAAC;SACH;aAAM;YACL,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAC1B;IACH,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CAAC,SAAyB;IAChD,OAAO,CAAC,MAA+B,EAAE,EAAE,CACzC,yBAAyB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,yBAAyB,CAChC,MAA+B,EAC/B,SAAyB;IAEzB,OAAO,UAAsC,GAAG,IAAW;QACzD,MAAM,CAAC,SAAS,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC;QACnC,MAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACpD,IACE,IAAA,oBAAW,EAAC,YAAY,CAAC;YACzB,IAAA,oBAAW,EAAC,SAAS,CAAC;YACtB,IAAA,oBAAW,EAAC,QAAQ,CAAC,EACrB;YACA,IAAI,QAAQ,KAAK,SAAS,EAAE;gBAC1B,MAAM,IAAI,qBAAc,CACtB,oFAAoF,CACrF,CAAC;aACH;YACD,MAAM,QAAQ,GAAG,IAAA,0BAAiB,EAAC,YAAY,CAAC,CAAC;YACjD,MAAM,MAAM,GAAG,IAAA,0BAAiB,EAAC,SAAS,CAAC,CAAC;YAC5C,MAAM,KAAK,GAAG,IAAA,0BAAiB,EAAC,QAAQ,CAAC,CAAC;YAC1C,SAAS,GAAG,CAAC,CAAS;gBACpB,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC;YACD,IAAI,CAAC,MAAM,CACT,GAAG,CAAC,QAAQ,GAAG,MAAM,CAAC,IAAI,KAAK,EAC/B,YAAY,QAAQ,mBAAmB,MAAM,QAAQ,KAAK,EAAE,EAC5D,YAAY,QAAQ,uBAAuB,MAAM,QAAQ,KAAK,EAAE,EAChE,QAAQ,EACR,oBAAoB,MAAM,GAAG,KAAK,QAAQ,MAAM,GAAG,KAAK,EAAE,CAC3D,CAAC;SACH;aAAM;YACL,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAC1B;IACH,CAAC,CAAC;AACJ,CAAC"}