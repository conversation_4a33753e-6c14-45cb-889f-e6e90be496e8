// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

/**
 * @title TokenRecoveryLib
 * @dev Library for handling token recovery operations
 */
library TokenRecoveryLib {
    
    struct RecoveryResult {
        bool success;
        uint256 amount;
        string errorMessage;
    }
    
    /**
     * @dev Safely attempt to recover tokens from one address to another
     * @param tokenAddress The address of the token contract
     * @param from The address to recover tokens from
     * @param to The address to send recovered tokens to
     * @return result The recovery result with success status and amount
     */
    function safeRecoverToken(
        address tokenAddress,
        address from,
        address to
    ) internal returns (RecoveryResult memory result) {
        if (tokenAddress == address(0) || from == address(0) || to == address(0)) {
            return RecoveryResult(false, 0, "Invalid address");
        }
        
        try IERC20(tokenAddress).balanceOf(from) returns (uint256 balance) {
            if (balance == 0) {
                return RecoveryResult(false, 0, "No balance");
            }
            
            try IERC20(tokenAddress).allowance(from, address(this)) returns (uint256 allowance) {
                if (allowance < balance) {
                    return RecoveryResult(false, balance, "Insufficient allowance");
                }
                
                try IERC20(tokenAddress).transferFrom(from, to, balance) {
                    return RecoveryResult(true, balance, "");
                } catch Error(string memory reason) {
                    return RecoveryResult(false, balance, reason);
                } catch {
                    return RecoveryResult(false, balance, "Transfer failed");
                }
            } catch {
                return RecoveryResult(false, balance, "Allowance check failed");
            }
        } catch {
            return RecoveryResult(false, 0, "Balance check failed");
        }
    }
    
    /**
     * @dev Check if a token contract exists and is valid
     * @param tokenAddress The address to check
     * @return isValid True if the address appears to be a valid token contract
     */
    function isValidTokenContract(address tokenAddress) internal view returns (bool isValid) {
        if (tokenAddress == address(0)) return false;
        
        // Check if the address has code
        uint256 size;
        assembly {
            size := extcodesize(tokenAddress)
        }
        
        if (size == 0) return false;
        
        // Try to call totalSupply() to verify it's an ERC20-like contract
        try IERC20(tokenAddress).totalSupply() returns (uint256) {
            return true;
        } catch {
            return false;
        }
    }
    
    /**
     * @dev Get the balance of a token for an address safely
     * @param tokenAddress The token contract address
     * @param account The account to check balance for
     * @return balance The token balance (0 if call fails)
     */
    function safeBalanceOf(address tokenAddress, address account) internal view returns (uint256 balance) {
        if (!isValidTokenContract(tokenAddress) || account == address(0)) {
            return 0;
        }
        
        try IERC20(tokenAddress).balanceOf(account) returns (uint256 bal) {
            return bal;
        } catch {
            return 0;
        }
    }
    
    /**
     * @dev Get the allowance of a token safely
     * @param tokenAddress The token contract address
     * @param owner The owner of the tokens
     * @param spender The spender address
     * @return allowance The allowance amount (0 if call fails)
     */
    function safeAllowance(
        address tokenAddress,
        address owner,
        address spender
    ) internal view returns (uint256 allowance) {
        if (!isValidTokenContract(tokenAddress) || owner == address(0) || spender == address(0)) {
            return 0;
        }
        
        try IERC20(tokenAddress).allowance(owner, spender) returns (uint256 allow) {
            return allow;
        } catch {
            return 0;
        }
    }
}
