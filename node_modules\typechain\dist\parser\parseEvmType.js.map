{"version": 3, "file": "parseEvmType.js", "sourceRoot": "", "sources": ["../../src/parser/parseEvmType.ts"], "names": [], "mappings": ";;;AAAA,4CAAwC;AACxC,mDAA+C;AA4C/C,MAAa,UAAU;IAIrB,YAAY,WAAmB,EAAE,UAAmB;QAClD,IAAI,CAAC,UAAU,GAAG,IAAA,6BAAa,EAAC,WAAW,CAAC,CAAA;QAC5C,IAAI,UAAU;YAAE,IAAI,CAAC,SAAS,GAAG,IAAA,6BAAa,EAAC,UAAU,CAAC,CAAA;IAC5D,CAAC;IAED,QAAQ;QACN,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAA;SAC9C;QACD,OAAO,IAAI,CAAC,UAAU,CAAA;IACxB,CAAC;IAED,KAAK,CAAC,KAA0B;QAC9B,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,CAAA;IAC/F,CAAC;CACF;AAnBD,gCAmBC;AAOD,MAAM,eAAe,GAAG,gBAAgB,CAAA;AACxC,MAAM,cAAc,GAAG,eAAe,CAAA;AACtC,MAAM,gBAAgB,GAAG,iBAAiB,CAAA;AAE1C,SAAgB,YAAY,CAAC,OAAe,EAAE,UAAwB,EAAE,YAAqB;IAC3F,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IAE5C,4BAA4B;IAC5B,IAAI,QAAQ,KAAK,GAAG,EAAE;QACpB,IAAI,oBAAoB,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAA;QAC7C,OAAO,OAAO,CAAC,oBAAoB,CAAC,KAAK,GAAG,EAAE;YAC5C,oBAAoB,EAAE,CAAA;SACvB;QAED,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC,oBAAoB,GAAG,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAChF,MAAM,SAAS,GAAG,YAAY,KAAK,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QAE1E,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,oBAAoB,CAAC,CAAA;QAE5D,MAAM,MAAM,GAAc;YACxB,IAAI,EAAE,OAAO;YACb,QAAQ,EAAE,YAAY,CAAC,aAAa,EAAE,UAAU,EAAE,YAAY,CAAC;YAC/D,YAAY,EAAE,OAAO;SACtB,CAAA;QACD,IAAI,SAAS;YAAE,MAAM,CAAC,IAAI,GAAG,SAAS,CAAA;QACtC,MAAM,UAAU,GAAG,4BAA4B,CAAC,YAAY,CAAC,CAAA;QAC7D,IAAI,UAAU;YAAE,MAAM,CAAC,UAAU,GAAG,UAAU,CAAA;QAE9C,OAAO,MAAM,CAAA;KACd;IAED,0CAA0C;IAE1C,kCAAkC;IAClC,QAAQ,OAAO,EAAE;QACf,KAAK,MAAM;YACT,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,CAAA;QACnD,KAAK,SAAS;YACZ,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,CAAA;QACnD,KAAK,QAAQ;YACX,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,CAAA;QAClD,KAAK,MAAM;YACT,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,YAAY,EAAE,OAAO,EAAE,CAAA;QAC1D,KAAK,OAAO;YACV,OAAO,EAAE,IAAI,EAAE,eAAe,EAAE,YAAY,EAAE,OAAO,EAAE,CAAA;QACzD,KAAK,OAAO;YACV,IAAI,CAAC,UAAU;gBAAE,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAA;YACvE,MAAM,MAAM,GAAc,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,EAAE,CAAA;YAC9E,MAAM,UAAU,GAAG,4BAA4B,CAAC,YAAY,CAAC,CAAA;YAC7D,IAAI,UAAU;gBAAE,MAAM,CAAC,UAAU,GAAG,UAAU,CAAA;YAC9C,OAAO,MAAM,CAAA;KAChB;IAED,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;QACjC,MAAM,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAC3C,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,QAAQ,CAAC,KAAM,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE,YAAY,EAAE,OAAO,EAAE,CAAA;KACvF;IAED,IAAI,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;QAChC,MAAM,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAC1C,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,CAAC,KAAM,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE,YAAY,EAAE,OAAO,EAAE,CAAA;KACtF;IAED,IAAI,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;QAClC,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAC5C,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,KAAM,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,EAAE,YAAY,EAAE,OAAO,EAAE,CAAA;KAClF;IAED,IAAI,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,UAAU,CAAC,MAAM,CAAC,EAAE;QACpC,OAAO,YAAY,CAAC,OAAO,CAAC,CAAA,CAAC,wJAAwJ;KACtL;IAED,IAAI,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,UAAU,CAAC,UAAU,CAAC,EAAE;QACxC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,CAAA;KAClD;IAED,eAAe;IACf,eAAM,CAAC,IAAI,CACT,yBAAyB,OAAO,wBAAwB,YAAY,4FAA4F,CACjK,CAAA;IAED,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,CAAA;AACnD,CAAC;AA9ED,oCA8EC;AAED,gBAAgB;AAChB,SAAgB,4BAA4B,CAAC,YAAgC;;IAC3E,IAAI,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,UAAU,CAAC,SAAS,CAAC,EAAE;QACvC,wCAAwC;QACxC,IAAI,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QAEnC,wCAAwC;QACxC,MAAM,WAAW,GAAG,MAAA,OAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,0CAAG,CAAC,CAAC,CAAA;QACzD,IAAI,WAAW,EAAE;YACf,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,CAAA;SAChE;QAED,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;YAC/B,MAAM,CAAC,SAAS,EAAE,UAAU,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAClD,OAAO,IAAI,UAAU,CAAC,UAAU,EAAE,SAAS,CAAC,CAAA;SAC7C;QAED,OAAO,IAAI,UAAU,CAAC,OAAO,CAAC,CAAA;KAC/B;AACH,CAAC;AAlBD,oEAkBC"}