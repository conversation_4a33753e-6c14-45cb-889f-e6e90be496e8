{"name": "restricted-transfer-token", "version": "1.0.0", "description": "ERC-20 token with transfer restrictions and multi-token recovery mechanism", "main": "index.js", "scripts": {"compile": "hardhat compile", "test": "hardhat test", "test:gas": "REPORT_GAS=true hardhat test", "deploy:sepolia": "hardhat run scripts/deploy.js --network sepolia", "verify:sepolia": "hardhat run scripts/verify.js --network sepolia", "node": "hardhat node", "clean": "hardhat clean"}, "keywords": ["ethereum", "solidity", "erc20", "hardhat", "sepolia"], "author": "Your Name", "license": "MIT", "devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.0", "@nomicfoundation/hardhat-ethers": "^3.0.0", "@nomicfoundation/hardhat-network-helpers": "^1.0.0", "@nomicfoundation/hardhat-toolbox": "^4.0.0", "@nomicfoundation/hardhat-verify": "^2.0.0", "@typechain/ethers-v6": "^0.5.0", "@typechain/hardhat": "^9.0.0", "@types/chai": "^4.2.0", "@types/mocha": ">=9.1.0", "chai": "^4.2.0", "ethers": "^6.4.0", "hardhat": "^2.19.0", "hardhat-gas-reporter": "^1.0.8", "solidity-coverage": "^0.8.1", "typechain": "^8.3.0", "typescript": ">=4.5.0"}, "dependencies": {"@openzeppelin/contracts": "^5.0.0", "dotenv": "^16.3.1"}}