{"name": "@nomicfoundation/hardhat-network-helpers", "version": "1.1.0", "description": "Hardhat utils for testing", "homepage": "https://github.com/nomicfoundation/hardhat/tree/main/packages/hardhat-network-helpers", "repository": "github:nomicfoundation/hardhat", "author": "Nomic Foundation", "license": "MIT", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "keywords": ["ethereum", "smart-contracts", "hardhat", "testing"], "files": ["dist/src/", "src/", "internal/", "types/", "*.d.ts", "*.d.ts.map", "*.js", "*.js.map", "LICENSE", "README.md"], "dependencies": {"ethereumjs-util": "^7.1.4"}, "devDependencies": {"@types/chai": "^4.2.0", "@types/chai-as-promised": "^7.1.3", "@types/mocha": ">=9.1.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "5.61.0", "@typescript-eslint/parser": "5.61.0", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "eslint": "^8.44.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-mocha": "10.4.1", "eslint-plugin-prettier": "3.4.0", "ethers-v5": "npm:ethers@5", "hardhat": "^2.26.0", "mocha": "^10.0.0", "prettier": "2.4.1", "rimraf": "^3.0.2", "ts-node": "^10.8.0", "typescript": "~5.0.0", "@nomicfoundation/eslint-plugin-hardhat-internal-rules": "^1.0.2", "@nomicfoundation/eslint-plugin-slow-imports": "^1.0.0"}, "peerDependencies": {"hardhat": "^2.26.0"}, "scripts": {"lint": "pnpm prettier --check && pnpm eslint", "lint:fix": "pnpm prettier --write && pnpm eslint --fix", "eslint": "eslint 'src/**/*.ts' 'test/**/*.ts'", "prettier": "prettier \"**/*.{js,md,json}\"", "pretest": "cd ../.. && pnpm build", "test": "mocha --recursive \"test/**/*.ts\" --exit", "build": "tsc --build .", "clean": "rimraf dist internal types *.{d.ts,js}{,.map} build-test tsconfig.tsbuildinfo"}}