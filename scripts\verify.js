const { run } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("Starting contract verification on Etherscan...");
  
  // Try to get contract address from environment or deployment files
  let contractAddress = process.env.TOKEN_CONTRACT_ADDRESS;
  let deploymentInfo = null;
  
  if (!contractAddress) {
    // Look for the latest deployment file
    const deploymentsDir = path.join(__dirname, "..", "deployments");
    if (fs.existsSync(deploymentsDir)) {
      const files = fs.readdirSync(deploymentsDir)
        .filter(file => file.startsWith("sepolia-") && file.endsWith(".json"))
        .sort()
        .reverse();
      
      if (files.length > 0) {
        const latestFile = path.join(deploymentsDir, files[0]);
        deploymentInfo = JSON.parse(fs.readFileSync(latestFile, "utf8"));
        contractAddress = deploymentInfo.contractAddress;
        console.log("Found contract address from deployment file:", contractAddress);
      }
    }
  }
  
  if (!contractAddress) {
    console.error("❌ Contract address not found. Please set TOKEN_CONTRACT_ADDRESS in .env or deploy first.");
    process.exit(1);
  }
  
  // Get constructor arguments
  let constructorArgs;
  
  if (deploymentInfo && deploymentInfo.tokenConfig) {
    const config = deploymentInfo.tokenConfig;
    constructorArgs = [
      config.name,
      config.symbol,
      config.initialSupply,
      config.initialOwner
    ];
  } else {
    // Default values - update these if different
    constructorArgs = [
      "RestrictedTransfer Token",
      "RTT",
      "1000000000000000000000000", // 1 million tokens in wei
      process.env.DEPLOYER_ADDRESS || "0x..." // Update with actual deployer address
    ];
    
    console.log("⚠️  Using default constructor arguments. Update if different:");
    console.log("Arguments:", constructorArgs);
  }
  
  try {
    console.log("🔍 Verifying contract at:", contractAddress);
    console.log("Constructor arguments:", constructorArgs);
    
    await run("verify:verify", {
      address: contractAddress,
      constructorArguments: constructorArgs,
    });
    
    console.log("✅ Contract verified successfully!");
    console.log("🔗 View on Etherscan:", `https://sepolia.etherscan.io/address/${contractAddress}#code`);
    
  } catch (error) {
    if (error.message.toLowerCase().includes("already verified")) {
      console.log("✅ Contract is already verified!");
      console.log("🔗 View on Etherscan:", `https://sepolia.etherscan.io/address/${contractAddress}#code`);
    } else {
      console.error("❌ Verification failed:", error.message);
      
      // Provide helpful debugging information
      console.log("\n🔧 Debugging information:");
      console.log("Contract Address:", contractAddress);
      console.log("Constructor Arguments:", constructorArgs);
      console.log("\n💡 Common issues:");
      console.log("1. Make sure ETHERSCAN_API_KEY is set in .env");
      console.log("2. Verify constructor arguments match deployment");
      console.log("3. Wait a few minutes after deployment before verifying");
      console.log("4. Check if contract is already verified");
      
      process.exit(1);
    }
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Verification script failed:", error);
    process.exit(1);
  });
