import chai from './index.js';

export const expect = chai.expect;
export const version = chai.version;
export const Assertion = chai.Assertion;
export const AssertionError = chai.AssertionError;
export const util = chai.util;
export const config = chai.config;
export const use = chai.use;
export const should = chai.should;
export const assert = chai.assert;
export const core = chai.core;

export default chai;
