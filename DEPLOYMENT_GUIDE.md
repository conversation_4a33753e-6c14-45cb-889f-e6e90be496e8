# Deployment Guide - RestrictedTransfer Token

This guide walks you through deploying the RestrictedTransfer Token to Ethereum's Sepolia testnet.

## 📋 Prerequisites

### 1. Required Accounts & Services
- **Ethereum Wallet** with Sepolia ETH (get from [Sepolia Faucet](https://sepoliafaucet.com/))
- **Infura Account** (or other RPC provider) - [Sign up here](https://infura.io/)
- **Etherscan Account** for contract verification - [Sign up here](https://etherscan.io/)

### 2. Minimum Requirements
- **Sepolia ETH**: ~0.01 ETH for deployment and testing
- **Node.js**: Version 16 or higher
- **Git**: For cloning the repository

## 🚀 Step-by-Step Deployment

### Step 1: Environment Setup

1. **Clone the repository**:
```bash
git clone <repository-url>
cd restricted-transfer-token
npm install
```

2. **Create environment file**:
```bash
cp .env.example .env
```

3. **Configure your .env file**:
```env
# Get from Infura dashboard
SEPOLIA_RPC_URL=https://sepolia.infura.io/v3/YOUR_INFURA_PROJECT_ID

# Your wallet private key (without 0x prefix)
# ⚠️ NEVER share this or commit to version control!
PRIVATE_KEY=your_private_key_here

# Get from Etherscan account settings
ETHERSCAN_API_KEY=your_etherscan_api_key_here

# Optional: Enable gas reporting
REPORT_GAS=true
```

### Step 2: Pre-Deployment Testing

1. **Compile contracts**:
```bash
npm run compile
```

2. **Run tests**:
```bash
npm test
```

3. **Check gas usage** (optional):
```bash
npm run test:gas
```

### Step 3: Deploy to Sepolia

1. **Deploy the contract**:
```bash
npm run deploy:sepolia
```

2. **Expected output**:
```
Starting deployment to Sepolia testnet...
Deploying contracts with account: 0x...
Account balance: 0.05 ETH

📋 Token Configuration:
Name: RestrictedTransfer Token
Symbol: RTT
Initial Supply: 1000000.0
Initial Owner: 0x...

🚀 Deploying RestrictedTransferToken...
✅ RestrictedTransferToken deployed to: 0x...

🔍 Verifying deployment...
Contract Name: RestrictedTransfer Token
Contract Symbol: RTT
Total Supply: 1000000.0
Owner: 0x...

💾 Deployment information saved to: deployments/sepolia-[timestamp].json
📝 Updated .env file with contract address

🎉 Deployment completed successfully!
```

### Step 4: Verify Contract

1. **Verify on Etherscan**:
```bash
npm run verify:sepolia
```

2. **Expected output**:
```
🔍 Verifying contract at: 0x...
✅ Contract verified successfully!
🔗 View on Etherscan: https://sepolia.etherscan.io/address/0x...#code
```

## ⚙️ Post-Deployment Configuration

### 1. Configure Common Tokens for Recovery

Add popular Sepolia testnet tokens to the recovery list:

```javascript
// Example script to add common tokens
const { ethers } = require("hardhat");

async function addCommonTokens() {
  const [deployer] = await ethers.getSigners();
  const tokenAddress = process.env.TOKEN_CONTRACT_ADDRESS;
  const token = await ethers.getContractAt("RestrictedTransferToken", tokenAddress);
  
  // Add common Sepolia tokens (replace with actual addresses)
  const commonTokens = [
    "0x...", // Sepolia USDC
    "0x...", // Sepolia USDT
    "0x...", // Sepolia DAI
  ];
  
  for (const tokenAddr of commonTokens) {
    await token.addCommonToken(tokenAddr);
    console.log(`Added token: ${tokenAddr}`);
  }
}

addCommonTokens().catch(console.error);
```

### 2. Set Up Exemptions

Exempt specific addresses from transfer restrictions:

```javascript
// Exempt exchanges, DEXs, or specific contracts
await token.setExemptFromRestrictions("0x...", true);
```

### 3. Initial Token Distribution

Distribute initial tokens to users:

```javascript
// Mint additional tokens if needed
await token.mint("0x...", ethers.parseEther("1000"));

// Or transfer from initial supply
await token.transfer("0x...", ethers.parseEther("1000"));
```

## 🧪 Testing Your Deployment

### 1. Basic Functionality Test

```javascript
const { ethers } = require("ethers");

// Connect to your deployed contract
const provider = new ethers.JsonRpcProvider(process.env.SEPOLIA_RPC_URL);
const wallet = new ethers.Wallet(process.env.PRIVATE_KEY, provider);
const token = new ethers.Contract(contractAddress, abi, wallet);

// Test basic functions
console.log("Name:", await token.name());
console.log("Symbol:", await token.symbol());
console.log("Total Supply:", ethers.formatEther(await token.totalSupply()));
console.log("Your Balance:", ethers.formatEther(await token.balanceOf(wallet.address)));
```

### 2. Transfer Restriction Test

1. **Send tokens to a test address**:
```javascript
await token.transfer(testAddress, ethers.parseEther("100"));
```

2. **Try to transfer to a third party** (should fail):
```javascript
// This should revert with TransferRestricted error
await token.connect(testWallet).transfer(thirdPartyAddress, ethers.parseEther("50"));
```

3. **Transfer back to original sender** (should succeed):
```javascript
await token.connect(testWallet).transfer(originalSender, ethers.parseEther("100"));
```

## 🔧 Troubleshooting

### Common Issues

1. **"Insufficient funds" error**:
   - Ensure you have enough Sepolia ETH
   - Check gas price settings in hardhat.config.js

2. **"Invalid API key" during verification**:
   - Verify your Etherscan API key is correct
   - Ensure the key has verification permissions

3. **"Contract already verified"**:
   - This is normal if verification was already successful
   - Check the Etherscan link to confirm

4. **RPC connection issues**:
   - Verify your Infura project ID is correct
   - Try a different RPC provider if needed

### Gas Optimization

If deployment costs are too high:

1. **Reduce optimizer runs** in hardhat.config.js:
```javascript
optimizer: {
  enabled: true,
  runs: 100, // Reduce from 200
}
```

2. **Check current gas prices**:
   - Use [ETH Gas Station](https://ethgasstation.info/) for current rates
   - Deploy during low-traffic periods

## 📊 Monitoring Your Contract

### 1. Etherscan Dashboard
- Monitor transactions: `https://sepolia.etherscan.io/address/YOUR_CONTRACT_ADDRESS`
- View events and logs
- Check contract interactions

### 2. Event Monitoring
Set up event listeners for important contract events:

```javascript
// Listen for restricted transfer attempts
token.on("RestrictedTransferAttempted", (from, intendedTo, redirectedTo, amount) => {
  console.log(`Restricted transfer: ${from} tried to send to ${intendedTo}, redirected to ${redirectedTo}`);
});

// Listen for token recovery attempts
token.on("TokenRecoveryAttempted", (from, to, tokenAddr, amount, success) => {
  console.log(`Recovery attempt: ${success ? 'Success' : 'Failed'} - ${amount} tokens`);
});
```

## 🔄 Upgrading and Maintenance

### Contract Updates
Since this contract is not upgradeable:
1. Deploy new version with improvements
2. Migrate token balances if needed
3. Update frontend/dApp to use new contract

### Regular Maintenance
- Monitor gas usage and optimize if needed
- Update common token list as new tokens are added to Sepolia
- Review and update exemption list
- Monitor for any unusual activity or edge cases

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review the test files for usage examples
3. Verify your environment configuration
4. Check Hardhat and OpenZeppelin documentation
5. Open an issue on the project repository

---

**🎉 Congratulations!** Your RestrictedTransfer Token is now deployed and ready for testing on Sepolia testnet.
