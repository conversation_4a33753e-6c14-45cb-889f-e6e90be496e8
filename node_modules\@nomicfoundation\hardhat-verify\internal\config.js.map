{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../src/internal/config.ts"], "names": [], "mappings": ";;;;;;AAQA,4DAAoC;AAEpC,SAAgB,uBAAuB,CACrC,MAAqB,EACrB,UAAuC;IAEvC,MAAM,sBAAsB,GAAoB;QAC9C,MAAM,EAAE,EAAE;QACV,YAAY,EAAE,EAAE;QAChB,OAAO,EAAE,IAAI;KACd,CAAC;IACF,MAAM,SAAS,GAAG,OAAO,CAAC,kBAAkB,CAA4B,CAAC;IACzE,MAAM,mBAAmB,GAAG,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;IAC5D,MAAM,CAAC,SAAS,GAAG,EAAE,GAAG,sBAAsB,EAAE,GAAG,mBAAmB,EAAE,CAAC;IAEzE,uEAAuE;IACvE,yCAAyC;IACzC,IACE,UAAU,CAAC,SAAS,KAAK,SAAS;QAClC,MAAM,CAAC,QAAQ,EAAE,SAAS,KAAK,SAAS,EACxC;QACA,OAAO,CAAC,IAAI,CACV,oBAAU,CAAC,MAAM,CACf,0MAA0M,CAC3M,CACF,CAAC;KACH;AACH,CAAC;AAzBD,0DAyBC;AAED,SAAgB,sBAAsB,CACpC,MAAqB,EACrB,UAAuC;IAEvC,MAAM,qBAAqB,GAAmB;QAC5C,OAAO,EAAE,KAAK;QACd,MAAM,EAAE,6BAA6B;QACrC,UAAU,EAAE,2BAA2B;KACxC,CAAC;IACF,MAAM,SAAS,GAAG,OAAO,CAAC,kBAAkB,CAA4B,CAAC;IACzE,MAAM,kBAAkB,GAAG,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC1D,MAAM,CAAC,QAAQ,GAAG,EAAE,GAAG,qBAAqB,EAAE,GAAG,kBAAkB,EAAE,CAAC;AACxE,CAAC;AAZD,wDAYC;AAED,SAAgB,wBAAwB,CACtC,MAAqB,EACrB,UAAuC;IAEvC,MAAM,uBAAuB,GAAqB;QAChD,OAAO,EAAE,KAAK;QACd,YAAY,EAAE,EAAE;KACjB,CAAC;IACF,MAAM,SAAS,GAAG,OAAO,CAAC,kBAAkB,CAA4B,CAAC;IACzE,MAAM,oBAAoB,GAAG,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;IAC9D,MAAM,CAAC,UAAU,GAAG,EAAE,GAAG,uBAAuB,EAAE,GAAG,oBAAoB,EAAE,CAAC;AAC9E,CAAC;AAXD,4DAWC"}