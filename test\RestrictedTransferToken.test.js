const { expect } = require("chai");
const { ethers } = require("hardhat");
const { loadFixture } = require("@nomicfoundation/hardhat-network-helpers");

describe("RestrictedTransferToken", function () {
  // Fixture for deploying the contract
  async function deployTokenFixture() {
    const [owner, addr1, addr2, addr3] = await ethers.getSigners();
    
    const initialSupply = ethers.parseEther("1000000");
    const RestrictedTransferToken = await ethers.getContractFactory("RestrictedTransferToken");
    const token = await RestrictedTransferToken.deploy(
      "RestrictedTransfer Token",
      "RTT",
      initialSupply,
      owner.address
    );
    
    return { token, owner, addr1, addr2, addr3, initialSupply };
  }
  
  // Fixture for deploying a mock ERC20 token for testing recovery
  async function deployMockTokenFixture() {
    const MockERC20 = await ethers.getContractFactory("MockERC20");
    const mockToken = await MockERC20.deploy("Mock Token", "MOCK", ethers.parseEther("1000000"));
    return { mockToken };
  }
  
  describe("Deployment", function () {
    it("Should deploy with correct initial values", async function () {
      const { token, owner, initialSupply } = await loadFixture(deployTokenFixture);
      
      expect(await token.name()).to.equal("RestrictedTransfer Token");
      expect(await token.symbol()).to.equal("RTT");
      expect(await token.totalSupply()).to.equal(initialSupply);
      expect(await token.balanceOf(owner.address)).to.equal(initialSupply);
      expect(await token.owner()).to.equal(owner.address);
    });
    
    it("Should set owner as exempt from restrictions", async function () {
      const { token, owner } = await loadFixture(deployTokenFixture);
      expect(await token.isExemptFromRestrictions(owner.address)).to.be.true;
    });
    
    it("Should emit TokenMinted event on deployment", async function () {
      const [owner] = await ethers.getSigners();
      const initialSupply = ethers.parseEther("1000000");
      
      const RestrictedTransferToken = await ethers.getContractFactory("RestrictedTransferToken");
      
      await expect(
        RestrictedTransferToken.deploy(
          "RestrictedTransfer Token",
          "RTT",
          initialSupply,
          owner.address
        )
      ).to.emit(RestrictedTransferToken, "TokenMinted")
       .withArgs(owner.address, initialSupply);
    });
  });
  
  describe("Basic ERC20 Functionality", function () {
    it("Should allow owner to transfer tokens freely", async function () {
      const { token, owner, addr1 } = await loadFixture(deployTokenFixture);
      const amount = ethers.parseEther("100");
      
      await expect(token.transfer(addr1.address, amount))
        .to.emit(token, "Transfer")
        .withArgs(owner.address, addr1.address, amount);
      
      expect(await token.balanceOf(addr1.address)).to.equal(amount);
    });
    
    it("Should allow approve and transferFrom", async function () {
      const { token, owner, addr1, addr2 } = await loadFixture(deployTokenFixture);
      const amount = ethers.parseEther("100");
      
      // Owner approves addr1 to spend tokens
      await token.approve(addr1.address, amount);
      expect(await token.allowance(owner.address, addr1.address)).to.equal(amount);
      
      // addr1 transfers from owner to addr2
      await expect(token.connect(addr1).transferFrom(owner.address, addr2.address, amount))
        .to.emit(token, "Transfer")
        .withArgs(owner.address, addr2.address, amount);
      
      expect(await token.balanceOf(addr2.address)).to.equal(amount);
    });
    
    it("Should allow minting by owner", async function () {
      const { token, owner, addr1 } = await loadFixture(deployTokenFixture);
      const amount = ethers.parseEther("100");
      
      await expect(token.mint(addr1.address, amount))
        .to.emit(token, "TokenMinted")
        .withArgs(addr1.address, amount);
      
      expect(await token.balanceOf(addr1.address)).to.equal(amount);
    });
    
    it("Should not allow minting by non-owner", async function () {
      const { token, addr1, addr2 } = await loadFixture(deployTokenFixture);
      const amount = ethers.parseEther("100");
      
      await expect(token.connect(addr1).mint(addr2.address, amount))
        .to.be.revertedWithCustomError(token, "OwnableUnauthorizedAccount");
    });
  });
  
  describe("Transfer Restrictions", function () {
    it("Should record original sender on first transfer", async function () {
      const { token, owner, addr1 } = await loadFixture(deployTokenFixture);
      const amount = ethers.parseEther("100");
      
      await expect(token.transfer(addr1.address, amount))
        .to.emit(token, "OriginalSenderRecorded")
        .withArgs(addr1.address, owner.address);
      
      expect(await token.originalSender(addr1.address)).to.equal(owner.address);
    });
    
    it("Should allow transfer back to original sender", async function () {
      const { token, owner, addr1 } = await loadFixture(deployTokenFixture);
      const amount = ethers.parseEther("100");
      
      // Owner sends to addr1
      await token.transfer(addr1.address, amount);
      
      // addr1 should be able to send back to owner
      await expect(token.connect(addr1).transfer(owner.address, amount))
        .to.emit(token, "Transfer")
        .withArgs(addr1.address, owner.address, amount);
      
      expect(await token.balanceOf(owner.address)).to.equal(await token.totalSupply());
      expect(await token.balanceOf(addr1.address)).to.equal(0);
    });
    
    it("Should restrict transfer to third party and redirect to original sender", async function () {
      const { token, owner, addr1, addr2 } = await loadFixture(deployTokenFixture);
      const amount = ethers.parseEther("100");
      
      // Owner sends to addr1
      await token.transfer(addr1.address, amount);
      
      // addr1 tries to send to addr2, should fail and redirect to owner
      await expect(token.connect(addr1).transfer(addr2.address, ethers.parseEther("50")))
        .to.be.revertedWithCustomError(token, "TransferRestricted")
        .withArgs(addr1.address, addr2.address, owner.address);
      
      // All tokens should be back with owner
      expect(await token.balanceOf(owner.address)).to.equal(await token.totalSupply());
      expect(await token.balanceOf(addr1.address)).to.equal(0);
      expect(await token.balanceOf(addr2.address)).to.equal(0);
    });
    
    it("Should emit RestrictedTransferAttempted event", async function () {
      const { token, owner, addr1, addr2 } = await loadFixture(deployTokenFixture);
      const amount = ethers.parseEther("100");
      
      await token.transfer(addr1.address, amount);
      
      await expect(token.connect(addr1).transfer(addr2.address, ethers.parseEther("50")))
        .to.emit(token, "RestrictedTransferAttempted")
        .withArgs(addr1.address, addr2.address, owner.address, ethers.parseEther("50"));
    });
    
    it("Should allow self-transfers", async function () {
      const { token, owner, addr1 } = await loadFixture(deployTokenFixture);
      const amount = ethers.parseEther("100");
      
      await token.transfer(addr1.address, amount);
      
      // Self-transfer should be allowed
      await expect(token.connect(addr1).transfer(addr1.address, amount))
        .to.emit(token, "Transfer")
        .withArgs(addr1.address, addr1.address, amount);
    });
  });
  
  describe("Exemption System", function () {
    it("Should allow owner to set exemptions", async function () {
      const { token, owner, addr1 } = await loadFixture(deployTokenFixture);
      
      await token.setExemptFromRestrictions(addr1.address, true);
      expect(await token.isExemptFromRestrictions(addr1.address)).to.be.true;
      
      await token.setExemptFromRestrictions(addr1.address, false);
      expect(await token.isExemptFromRestrictions(addr1.address)).to.be.false;
    });
    
    it("Should allow exempt addresses to transfer freely", async function () {
      const { token, owner, addr1, addr2, addr3 } = await loadFixture(deployTokenFixture);
      const amount = ethers.parseEther("100");
      
      // Set addr1 as exempt
      await token.setExemptFromRestrictions(addr1.address, true);
      
      // Owner sends to addr1
      await token.transfer(addr1.address, amount);
      
      // addr1 (exempt) should be able to send to addr2
      await expect(token.connect(addr1).transfer(addr2.address, amount))
        .to.emit(token, "Transfer")
        .withArgs(addr1.address, addr2.address, amount);
      
      expect(await token.balanceOf(addr2.address)).to.equal(amount);
    });
    
    it("Should not allow non-owner to set exemptions", async function () {
      const { token, addr1, addr2 } = await loadFixture(deployTokenFixture);
      
      await expect(token.connect(addr1).setExemptFromRestrictions(addr2.address, true))
        .to.be.revertedWithCustomError(token, "OwnableUnauthorizedAccount");
    });
  });
  
  describe("Pause Functionality", function () {
    it("Should allow owner to pause and unpause", async function () {
      const { token, owner } = await loadFixture(deployTokenFixture);
      
      await token.pause();
      expect(await token.paused()).to.be.true;
      
      await token.unpause();
      expect(await token.paused()).to.be.false;
    });
    
    it("Should prevent transfers when paused", async function () {
      const { token, owner, addr1 } = await loadFixture(deployTokenFixture);
      const amount = ethers.parseEther("100");
      
      await token.pause();
      
      await expect(token.transfer(addr1.address, amount))
        .to.be.revertedWithCustomError(token, "EnforcedPause");
    });
    
    it("Should not allow non-owner to pause", async function () {
      const { token, addr1 } = await loadFixture(deployTokenFixture);
      
      await expect(token.connect(addr1).pause())
        .to.be.revertedWithCustomError(token, "OwnableUnauthorizedAccount");
    });
  });
  
  describe("Common Tokens Management", function () {
    it("Should allow owner to add and remove common tokens", async function () {
      const { token, owner } = await loadFixture(deployTokenFixture);
      const tokenAddress = "******************************************";
      
      await token.addCommonToken(tokenAddress);
      const commonTokens = await token.getCommonTokens();
      expect(commonTokens).to.include(tokenAddress);
      
      await token.removeCommonToken(tokenAddress);
      const updatedTokens = await token.getCommonTokens();
      expect(updatedTokens).to.not.include(tokenAddress);
    });
    
    it("Should not allow non-owner to manage common tokens", async function () {
      const { token, addr1 } = await loadFixture(deployTokenFixture);
      const tokenAddress = "******************************************";
      
      await expect(token.connect(addr1).addCommonToken(tokenAddress))
        .to.be.revertedWithCustomError(token, "OwnableUnauthorizedAccount");
    });
  });
  
  describe("Error Handling", function () {
    it("Should revert on zero address transfers", async function () {
      const { token } = await loadFixture(deployTokenFixture);
      const amount = ethers.parseEther("100");
      
      await expect(token.transfer(ethers.ZeroAddress, amount))
        .to.be.revertedWithCustomError(token, "ZeroAddress");
    });
    
    it("Should revert on zero amount transfers", async function () {
      const { token, addr1 } = await loadFixture(deployTokenFixture);
      
      await expect(token.transfer(addr1.address, 0))
        .to.be.revertedWithCustomError(token, "ZeroAmount");
    });
    
    it("Should revert on insufficient balance", async function () {
      const { token, addr1, addr2 } = await loadFixture(deployTokenFixture);
      const amount = ethers.parseEther("100");
      
      await expect(token.connect(addr1).transfer(addr2.address, amount))
        .to.be.revertedWithCustomError(token, "ERC20InsufficientBalance");
    });
  });
  
  describe("Emergency Functions", function () {
    it("Should allow owner to recover tokens sent to contract", async function () {
      const { token, owner, addr1 } = await loadFixture(deployTokenFixture);
      const amount = ethers.parseEther("100");
      
      // Send tokens to the contract address
      await token.transfer(await token.getAddress(), amount);
      
      // Owner should be able to recover them
      await expect(token.emergencyRecoverToken(await token.getAddress(), addr1.address, amount))
        .to.emit(token, "Transfer")
        .withArgs(await token.getAddress(), addr1.address, amount);
    });
    
    it("Should allow contract to receive ETH", async function () {
      const { token, owner } = await loadFixture(deployTokenFixture);
      const amount = ethers.parseEther("1");
      
      await expect(
        owner.sendTransaction({
          to: await token.getAddress(),
          value: amount
        })
      ).to.not.be.reverted;
    });
  });
});
