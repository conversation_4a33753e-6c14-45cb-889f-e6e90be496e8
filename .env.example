# Sepolia testnet RPC URL (get from Infura, Alchemy, or other provider)
SEPOLIA_RPC_URL=https://sepolia.infura.io/v3/YOUR_INFURA_PROJECT_ID

# Private key of the deployer account (without 0x prefix)
# NEVER commit your real private key to version control!
PRIVATE_KEY=your_private_key_here

# Etherscan API key for contract verification
ETHERSCAN_API_KEY=your_etherscan_api_key_here

# Gas reporting (set to true to enable gas usage reports)
REPORT_GAS=false

# Contract deployment addresses (filled after deployment)
TOKEN_CONTRACT_ADDRESS=
