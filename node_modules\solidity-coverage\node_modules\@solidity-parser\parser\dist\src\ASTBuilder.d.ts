import { ParserRuleContext, ParseTreeVisitor } from 'antlr4';
import * as SP from './antlr/SolidityParser';
import SolidityVisitor from './antlr/SolidityVisitor';
import { ParseOptions } from './types';
import * as AST from './ast-types';
interface WithMeta {
    __withMeta: never;
}
type ASTBuilderNode = AST.ASTNode & WithMeta;
export declare class ASTBuilder extends ParseTreeVisitor<ASTBuilderNode> implements SolidityVisitor<ASTBuilderNode | ASTBuilderNode[]> {
    options: ParseOptions;
    result: AST.SourceUnit | null;
    private _currentContract?;
    constructor(options: ParseOptions);
    defaultResult(): AST.ASTNode & WithMeta;
    aggregateResult(): AST.ASTNode & WithMeta;
    visitSourceUnit(ctx: SP.SourceUnitContext): AST.SourceUnit & WithMeta;
    visitContractPart(ctx: SP.ContractPartContext): ASTBuilderNode;
    visitContractDefinition(ctx: SP.ContractDefinitionContext): AST.ContractDefinition & WithMeta;
    visitStateVariableDeclaration(ctx: SP.StateVariableDeclarationContext): AST.StateVariableDeclaration & WithMeta;
    visitVariableDeclaration(ctx: SP.VariableDeclarationContext): AST.VariableDeclaration & WithMeta;
    visitVariableDeclarationStatement(ctx: SP.VariableDeclarationStatementContext): AST.VariableDeclarationStatement & WithMeta;
    visitStatement(ctx: SP.StatementContext): AST.Statement & WithMeta;
    visitSimpleStatement(ctx: SP.SimpleStatementContext): AST.SimpleStatement & WithMeta;
    visitEventDefinition(ctx: SP.EventDefinitionContext): AST.EventDefinition & WithMeta;
    visitBlock(ctx: SP.BlockContext): AST.Block & WithMeta;
    visitParameter(ctx: SP.ParameterContext): AST.VariableDeclaration & WithMeta;
    visitFunctionDefinition(ctx: SP.FunctionDefinitionContext): AST.FunctionDefinition & WithMeta;
    visitEnumDefinition(ctx: SP.EnumDefinitionContext): AST.EnumDefinition & WithMeta;
    visitEnumValue(ctx: SP.EnumValueContext): AST.EnumValue & WithMeta;
    visitElementaryTypeName(ctx: SP.ElementaryTypeNameContext): AST.ElementaryTypeName & WithMeta;
    visitIdentifier(ctx: SP.IdentifierContext): AST.Identifier & WithMeta;
    visitTypeName(ctx: SP.TypeNameContext): AST.TypeName & WithMeta;
    visitUserDefinedTypeName(ctx: SP.UserDefinedTypeNameContext): AST.UserDefinedTypeName & WithMeta;
    visitUsingForDeclaration(ctx: SP.UsingForDeclarationContext): AST.UsingForDeclaration & WithMeta;
    visitPragmaDirective(ctx: SP.PragmaDirectiveContext): AST.PragmaDirective & WithMeta;
    visitInheritanceSpecifier(ctx: SP.InheritanceSpecifierContext): AST.InheritanceSpecifier & WithMeta;
    visitModifierInvocation(ctx: SP.ModifierInvocationContext): AST.ModifierInvocation & WithMeta;
    visitFunctionTypeName(ctx: SP.FunctionTypeNameContext): AST.FunctionTypeName & WithMeta;
    visitFunctionTypeParameter(ctx: SP.FunctionTypeParameterContext): AST.VariableDeclaration & WithMeta;
    visitThrowStatement(ctx: SP.ThrowStatementContext): AST.ThrowStatement & WithMeta;
    visitReturnStatement(ctx: SP.ReturnStatementContext): AST.ReturnStatement & WithMeta;
    visitEmitStatement(ctx: SP.EmitStatementContext): AST.EmitStatement & WithMeta;
    visitCustomErrorDefinition(ctx: SP.CustomErrorDefinitionContext): AST.CustomErrorDefinition & WithMeta;
    visitTypeDefinition(ctx: SP.TypeDefinitionContext): AST.TypeDefinition & WithMeta;
    visitRevertStatement(ctx: SP.RevertStatementContext): AST.RevertStatement & WithMeta;
    visitFunctionCall(ctx: SP.FunctionCallContext): AST.FunctionCall & WithMeta;
    visitStructDefinition(ctx: SP.StructDefinitionContext): AST.StructDefinition & WithMeta;
    visitWhileStatement(ctx: SP.WhileStatementContext): AST.WhileStatement & WithMeta;
    visitDoWhileStatement(ctx: SP.DoWhileStatementContext): AST.DoWhileStatement & WithMeta;
    visitIfStatement(ctx: SP.IfStatementContext): AST.IfStatement & WithMeta;
    visitTryStatement(ctx: SP.TryStatementContext): AST.TryStatement & WithMeta;
    visitCatchClause(ctx: SP.CatchClauseContext): AST.CatchClause & WithMeta;
    visitExpressionStatement(ctx: SP.ExpressionStatementContext): AST.ExpressionStatement & WithMeta;
    visitNumberLiteral(ctx: SP.NumberLiteralContext): AST.NumberLiteral & WithMeta;
    visitMappingKey(ctx: SP.MappingKeyContext): (AST.ElementaryTypeName | AST.UserDefinedTypeName) & WithMeta;
    visitMapping(ctx: SP.MappingContext): AST.Mapping & WithMeta;
    visitModifierDefinition(ctx: SP.ModifierDefinitionContext): AST.ModifierDefinition & WithMeta;
    visitUncheckedStatement(ctx: SP.UncheckedStatementContext): AST.UncheckedStatement & WithMeta;
    visitExpression(ctx: SP.ExpressionContext): AST.Expression & WithMeta;
    visitNameValueList(ctx: SP.NameValueListContext): AST.NameValueList & WithMeta;
    visitFileLevelConstant(ctx: SP.FileLevelConstantContext): AST.FileLevelConstant & WithMeta;
    visitForStatement(ctx: SP.ForStatementContext): AST.ForStatement & WithMeta;
    visitHexLiteral(ctx: SP.HexLiteralContext): AST.HexLiteral & WithMeta;
    visitPrimaryExpression(ctx: SP.PrimaryExpressionContext): AST.PrimaryExpression & WithMeta;
    visitTupleExpression(ctx: SP.TupleExpressionContext): AST.TupleExpression & WithMeta;
    buildIdentifierList(ctx: SP.IdentifierListContext): ((AST.VariableDeclaration & WithMeta) | null)[];
    buildVariableDeclarationList(ctx: SP.VariableDeclarationListContext): Array<(AST.VariableDeclaration & WithMeta) | null>;
    visitImportDirective(ctx: SP.ImportDirectiveContext): AST.ImportDirective & WithMeta;
    buildEventParameterList(ctx: SP.EventParameterListContext): {
        type: string;
        typeName: ASTBuilderNode;
        name: string | null;
        isStateVar: boolean;
        isIndexed: boolean;
    }[];
    visitReturnParameters(ctx: SP.ReturnParametersContext): (AST.VariableDeclaration & WithMeta)[];
    visitParameterList(ctx: SP.ParameterListContext): (AST.VariableDeclaration & WithMeta)[];
    visitInlineAssemblyStatement(ctx: SP.InlineAssemblyStatementContext): AST.InlineAssemblyStatement & WithMeta;
    visitAssemblyBlock(ctx: SP.AssemblyBlockContext): AST.AssemblyBlock & WithMeta;
    visitAssemblyItem(ctx: SP.AssemblyItemContext): AST.AssemblyItem & WithMeta;
    visitAssemblyExpression(ctx: SP.AssemblyExpressionContext): AST.AssemblyExpression & WithMeta;
    visitAssemblyCall(ctx: SP.AssemblyCallContext): AST.AssemblyCall & WithMeta;
    visitAssemblyLiteral(ctx: SP.AssemblyLiteralContext): AST.AssemblyLiteral & WithMeta;
    visitAssemblySwitch(ctx: SP.AssemblySwitchContext): AST.AssemblySwitch & WithMeta;
    visitAssemblyCase(ctx: SP.AssemblyCaseContext): AST.AssemblyCase & WithMeta;
    visitAssemblyLocalDefinition(ctx: SP.AssemblyLocalDefinitionContext): AST.AssemblyLocalDefinition & WithMeta;
    visitAssemblyFunctionDefinition(ctx: SP.AssemblyFunctionDefinitionContext): AST.AssemblyFunctionDefinition & WithMeta;
    visitAssemblyAssignment(ctx: SP.AssemblyAssignmentContext): AST.AssemblyAssignment & WithMeta;
    visitAssemblyMember(ctx: SP.AssemblyMemberContext): AST.AssemblyMemberAccess & WithMeta;
    visitLabelDefinition(ctx: SP.LabelDefinitionContext): AST.LabelDefinition & WithMeta;
    visitAssemblyStackAssignment(ctx: SP.AssemblyStackAssignmentContext): AST.AssemblyStackAssignment & WithMeta;
    visitAssemblyFor(ctx: SP.AssemblyForContext): AST.AssemblyFor & WithMeta;
    visitAssemblyIf(ctx: SP.AssemblyIfContext): AST.AssemblyIf & WithMeta;
    visitContinueStatement(ctx: SP.ContinueStatementContext): AST.ContinueStatement & WithMeta;
    visitBreakStatement(ctx: SP.BreakStatementContext): AST.BreakStatement & WithMeta;
    private _toText;
    private _stateMutabilityToText;
    private _loc;
    _range(ctx: ParserRuleContext): [number, number];
    private _addMeta;
    private _mapCommasToNulls;
}
export {};
