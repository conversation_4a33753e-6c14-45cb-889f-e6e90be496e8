{"_format": "hh-sol-artifact-1", "contractName": "ERC1967Utils", "sourceName": "contracts/proxy/ERC1967/ERC1967Utils.sol", "abi": [{"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "ERC1967InvalidAdmin", "type": "error"}, {"inputs": [{"internalType": "address", "name": "beacon", "type": "address"}], "name": "ERC1967InvalidBeacon", "type": "error"}, {"inputs": [{"internalType": "address", "name": "implementation", "type": "address"}], "name": "ERC1967InvalidImplementation", "type": "error"}, {"inputs": [], "name": "ERC1967Non<PERSON>ayable", "type": "error"}], "bytecode": "0x60556032600b8282823980515f1a607314602657634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220632ade5569700b1b6c0d5a63cf8d1120358a3b85cac6f2938ae615b83b0b999c64736f6c634300081b0033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040525f5ffdfea2646970667358221220632ade5569700b1b6c0d5a63cf8d1120358a3b85cac6f2938ae615b83b0b999c64736f6c634300081b0033", "linkReferences": {}, "deployedLinkReferences": {}}