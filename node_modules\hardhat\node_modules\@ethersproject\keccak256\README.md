KECCAK256 Hash Function
=======================

This sub-module is part of the [ethers project](https://github.com/ethers-io/ethers.js).

It is responsible for the identify function (i.e. KECCAK256) use in Ethereum.

For more information, see the [documentation](https://docs.ethers.io/v5/api/utils/hashing/#utils-keccak256).


Importing
---------

Most users will prefer to use the [umbrella package](https://www.npmjs.com/package/ethers),
but for those with more specific needs, individual components can be imported.

```javascript
const {

    keccak256

} = require("@ethersproject/keccak256");
```


License
-------

MIT License
