const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("Starting deployment to Sepolia testnet...");
  
  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying contracts with account:", deployer.address);
  
  // Check deployer balance
  const balance = await ethers.provider.getBalance(deployer.address);
  console.log("Account balance:", ethers.formatEther(balance), "ETH");
  
  if (balance < ethers.parseEther("0.01")) {
    console.warn("⚠️  Warning: Low balance. Make sure you have enough ETH for deployment.");
  }
  
  // Token configuration
  const TOKEN_CONFIG = {
    name: "RestrictedTransfer Token",
    symbol: "RTT",
    initialSupply: ethers.parseEther("1000000"), // 1 million tokens
    initialOwner: deployer.address
  };
  
  console.log("\n📋 Token Configuration:");
  console.log("Name:", TOKEN_CONFIG.name);
  console.log("Symbol:", TOKEN_CONFIG.symbol);
  console.log("Initial Supply:", ethers.formatEther(TOKEN_CONFIG.initialSupply));
  console.log("Initial Owner:", TOKEN_CONFIG.initialOwner);
  
  try {
    // Deploy the RestrictedTransferToken contract
    console.log("\n🚀 Deploying RestrictedTransferToken...");
    const RestrictedTransferToken = await ethers.getContractFactory("RestrictedTransferToken");
    
    const token = await RestrictedTransferToken.deploy(
      TOKEN_CONFIG.name,
      TOKEN_CONFIG.symbol,
      TOKEN_CONFIG.initialSupply,
      TOKEN_CONFIG.initialOwner
    );
    
    await token.waitForDeployment();
    const tokenAddress = await token.getAddress();
    
    console.log("✅ RestrictedTransferToken deployed to:", tokenAddress);
    
    // Verify deployment
    console.log("\n🔍 Verifying deployment...");
    const name = await token.name();
    const symbol = await token.symbol();
    const totalSupply = await token.totalSupply();
    const owner = await token.owner();
    
    console.log("Contract Name:", name);
    console.log("Contract Symbol:", symbol);
    console.log("Total Supply:", ethers.formatEther(totalSupply));
    console.log("Owner:", owner);
    
    // Add some common tokens for recovery (example addresses for Sepolia)
    console.log("\n⚙️  Configuring common tokens for recovery...");
    
    // These are example token addresses on Sepolia - replace with actual tokens you want to support
    const commonTokens = [
      // Add real Sepolia token addresses here
      // "0x...", // Example: USDC on Sepolia
      // "0x...", // Example: USDT on Sepolia
    ];
    
    for (const tokenAddr of commonTokens) {
      try {
        await token.addCommonToken(tokenAddr);
        console.log("Added common token:", tokenAddr);
      } catch (error) {
        console.log("Failed to add token:", tokenAddr, error.message);
      }
    }
    
    // Save deployment information
    const deploymentInfo = {
      network: "sepolia",
      contractAddress: tokenAddress,
      deployerAddress: deployer.address,
      deploymentTime: new Date().toISOString(),
      transactionHash: token.deploymentTransaction().hash,
      blockNumber: token.deploymentTransaction().blockNumber,
      gasUsed: token.deploymentTransaction().gasLimit.toString(),
      tokenConfig: TOKEN_CONFIG,
      commonTokens: commonTokens
    };
    
    // Create deployments directory if it doesn't exist
    const deploymentsDir = path.join(__dirname, "..", "deployments");
    if (!fs.existsSync(deploymentsDir)) {
      fs.mkdirSync(deploymentsDir, { recursive: true });
    }
    
    // Save deployment info to file
    const deploymentFile = path.join(deploymentsDir, `sepolia-${Date.now()}.json`);
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
    
    console.log("\n💾 Deployment information saved to:", deploymentFile);
    
    // Update .env file with contract address
    const envPath = path.join(__dirname, "..", ".env");
    let envContent = "";
    
    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, "utf8");
    }
    
    // Update or add TOKEN_CONTRACT_ADDRESS
    if (envContent.includes("TOKEN_CONTRACT_ADDRESS=")) {
      envContent = envContent.replace(
        /TOKEN_CONTRACT_ADDRESS=.*/,
        `TOKEN_CONTRACT_ADDRESS=${tokenAddress}`
      );
    } else {
      envContent += `\nTOKEN_CONTRACT_ADDRESS=${tokenAddress}\n`;
    }
    
    fs.writeFileSync(envPath, envContent);
    console.log("📝 Updated .env file with contract address");
    
    console.log("\n🎉 Deployment completed successfully!");
    console.log("\n📋 Summary:");
    console.log("Contract Address:", tokenAddress);
    console.log("Network: Sepolia Testnet");
    console.log("Explorer URL:", `https://sepolia.etherscan.io/address/${tokenAddress}`);
    
    console.log("\n🔧 Next steps:");
    console.log("1. Verify the contract on Etherscan:");
    console.log(`   npx hardhat verify --network sepolia ${tokenAddress} "${TOKEN_CONFIG.name}" "${TOKEN_CONFIG.symbol}" "${TOKEN_CONFIG.initialSupply}" "${TOKEN_CONFIG.initialOwner}"`);
    console.log("2. Run tests to ensure everything works:");
    console.log("   npm test");
    console.log("3. Add the contract address to your frontend/dApp");
    
  } catch (error) {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  }
}

// Handle errors
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment script failed:", error);
    process.exit(1);
  });
